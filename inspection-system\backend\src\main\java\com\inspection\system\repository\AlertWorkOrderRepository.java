package com.inspection.system.repository;

import com.inspection.system.entity.AlertWorkOrder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 告警工单数据访问层
 */
@Repository
public interface AlertWorkOrderRepository extends JpaRepository<AlertWorkOrder, Long> {

    /**
     * 根据工单编码查询工单
     */
    Optional<AlertWorkOrder> findByOrderCode(String orderCode);

    /**
     * 根据任务ID查询工单列表
     */
    List<AlertWorkOrder> findByTaskId(Long taskId);

    /**
     * 根据告警级别查询工单列表
     */
    List<AlertWorkOrder> findByAlertLevel(AlertWorkOrder.AlertLevel alertLevel);

    /**
     * 根据工单状态查询工单列表
     */
    List<AlertWorkOrder> findByStatus(AlertWorkOrder.OrderStatus status);

    /**
     * 根据处理人查询工单列表
     */
    List<AlertWorkOrder> findByAssignee(String assignee);

    /**
     * 根据告警类型查询工单列表
     */
    List<AlertWorkOrder> findByAlertType(String alertType);

    /**
     * 根据目标类型和目标ID查询工单列表
     */
    List<AlertWorkOrder> findByTargetTypeAndTargetId(AlertWorkOrder.TargetType targetType, Long targetId);

    /**
     * 查询指定时间范围内创建的工单
     */
    List<AlertWorkOrder> findByCreateTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询待处理的工单
     */
    @Query("SELECT o FROM AlertWorkOrder o WHERE o.status IN ('OPEN', 'PROCESSING') ORDER BY o.alertLevel DESC, o.createTime ASC")
    List<AlertWorkOrder> findPendingOrders();

    /**
     * 查询最近的告警工单
     */
    @Query("SELECT o FROM AlertWorkOrder o ORDER BY o.createTime DESC")
    Page<AlertWorkOrder> findRecentOrders(Pageable pageable);

    /**
     * 统计各级别告警数量
     */
    @Query("SELECT o.alertLevel, COUNT(o) FROM AlertWorkOrder o GROUP BY o.alertLevel")
    List<Object[]> countByAlertLevel();

    /**
     * 根据告警级别统计数量
     */
    @Query("SELECT COUNT(o) FROM AlertWorkOrder o WHERE o.alertLevel = :alertLevel")
    Long countByAlertLevel(@Param("alertLevel") String alertLevel);

    /**
     * 统计各状态工单数量
     */
    @Query("SELECT o.status, COUNT(o) FROM AlertWorkOrder o GROUP BY o.status")
    List<Object[]> countByStatus();

    /**
     * 统计各告警类型数量
     */
    @Query("SELECT o.alertType, COUNT(o) FROM AlertWorkOrder o GROUP BY o.alertType")
    List<Object[]> countByAlertType();

    /**
     * 查询今日新增告警数
     */
    @Query("SELECT COUNT(o) FROM AlertWorkOrder o WHERE o.createTime >= CURRENT_DATE")
    Long countTodayAlerts();

    /**
     * 查询待处理告警数
     */
    @Query("SELECT COUNT(o) FROM AlertWorkOrder o WHERE o.status IN ('OPEN', 'PROCESSING')")
    Long countPendingAlerts();

    /**
     * 查询严重告警数
     */
    @Query("SELECT COUNT(o) FROM AlertWorkOrder o WHERE o.alertLevel = 'CRITICAL' AND o.status IN ('OPEN', 'PROCESSING')")
    Long countCriticalAlerts();

    /**
     * 检查工单编码是否存在
     */
    boolean existsByOrderCode(String orderCode);

    /**
     * 多条件分页查询工单
     */
    @Query("SELECT o FROM AlertWorkOrder o WHERE " +
           "(:orderCode IS NULL OR o.orderCode LIKE %:orderCode%) AND " +
           "(:alertLevel IS NULL OR o.alertLevel = :alertLevel) AND " +
           "(:status IS NULL OR o.status = :status) AND " +
           "(:alertType IS NULL OR o.alertType LIKE %:alertType%) AND " +
           "(:assignee IS NULL OR o.assignee LIKE %:assignee%) AND " +
           "(:targetType IS NULL OR o.targetType = :targetType) AND " +
           "(:startTime IS NULL OR o.createTime >= :startTime) AND " +
           "(:endTime IS NULL OR o.createTime <= :endTime)")
    Page<AlertWorkOrder> findByConditions(@Param("orderCode") String orderCode,
                                         @Param("alertLevel") AlertWorkOrder.AlertLevel alertLevel,
                                         @Param("status") AlertWorkOrder.OrderStatus status,
                                         @Param("alertType") String alertType,
                                         @Param("assignee") String assignee,
                                         @Param("targetType") AlertWorkOrder.TargetType targetType,
                                         @Param("startTime") LocalDateTime startTime,
                                         @Param("endTime") LocalDateTime endTime,
                                         Pageable pageable);

    /**
     * 查询告警趋势数据（按日期统计）
     */
    @Query("SELECT CAST(DATE(o.createTime) AS STRING) as date, COUNT(o) as count " +
           "FROM AlertWorkOrder o " +
           "WHERE o.createTime >= :startDate " +
           "GROUP BY DATE(o.createTime) " +
           "ORDER BY DATE(o.createTime)")
    List<Object[]> getAlertTrendData(@Param("startDate") LocalDateTime startDate);

    /**
     * 查询需要升级的待处理告警
     */
    @Query("SELECT o FROM AlertWorkOrder o WHERE o.status IN ('OPEN', 'PROCESSING') " +
           "AND o.createTime <= :escalationThreshold " +
           "ORDER BY o.alertLevel DESC, o.createTime ASC")
    List<AlertWorkOrder> findPendingAlertsForEscalation(@Param("escalationThreshold") LocalDateTime escalationThreshold);
}