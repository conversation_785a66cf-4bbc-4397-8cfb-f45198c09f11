{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents\\\\\\u6D69\\u9CB8\\\\ai\\u57F9\\u8BAD\\\\AI\\u8D4B\\u80FD\\u8D44\\u6599(2)\\\\\\u4EFB\\u52A13-\\u4F5C\\u4E1A\\u8BA1\\u5212\\u5DE1\\u68C0\\\\inspection-system\\\\frontend\\\\src\\\\pages\\\\Home.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Card, Row, Col, Typography, Button } from 'antd';\nimport { DashboardOutlined, UnorderedListOutlined, SearchOutlined, SettingOutlined, AlertOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Paragraph\n} = Typography;\nconst FeatureCard = ({\n  icon,\n  title,\n  description,\n  features,\n  path,\n  color\n}) => {\n  _s();\n  const navigate = useNavigate();\n  return /*#__PURE__*/_jsxDEV(Card, {\n    hoverable: true,\n    className: \"feature-card\",\n    style: {\n      height: '100%'\n    },\n    onClick: () => navigate(path),\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card-header\",\n      style: {\n        background: color\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-icon\",\n        children: icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card-content\",\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 4,\n        className: \"card-title\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n        className: \"card-description\",\n        children: description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        className: \"card-features\",\n        children: features.map((feature, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n          children: feature\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        className: \"modern-button\",\n        block: true,\n        children: \"\\u67E5\\u770B\\u8BE6\\u60C5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 5\n  }, this);\n};\n_s(FeatureCard, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = FeatureCard;\nconst Home = () => {\n  _s2();\n  const navigate = useNavigate();\n  const featureCards = [{\n    icon: /*#__PURE__*/_jsxDEV(DashboardOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 13\n    }, this),\n    title: '仪表盘',\n    description: '系统概览页面，展示关键统计信息和实时数据',\n    features: ['统计卡片展示', '图表可视化', '最近任务列表', '告警信息展示'],\n    path: '/dashboard',\n    color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(UnorderedListOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 13\n    }, this),\n    title: '巡检任务',\n    description: '巡检任务管理页面，支持任务创建、执行和监控',\n    features: ['任务列表展示', '多条件筛选', '状态管理', '批量操作'],\n    path: '/tasks',\n    color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 13\n    }, this),\n    title: '任务详情',\n    description: '任务详细信息页面，包含巡检结果和轨迹记录',\n    features: ['任务基本信息', '巡检结果详情', '轨迹时间线', '相关告警'],\n    path: '/tasks',\n    color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 13\n    }, this),\n    title: '配置管理',\n    description: '巡检配置管理页面，支持配置创建和调度设置',\n    features: ['配置列表管理', '启用/禁用开关', '新建配置', '频率设置'],\n    path: '/config',\n    color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(AlertOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 13\n    }, this),\n    title: '告警工单',\n    description: '告警工单管理页面，处理异常告警和工单流程',\n    features: ['告警统计', '告警列表', '处理状态', '告警升级'],\n    path: '/alerts',\n    color: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"home-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"home-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"home-logo\",\n        children: \"\\u8BBE\\u5907\\u5DE1\\u68C0\\u7BA1\\u7406\\u7CFB\\u7EDF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"home-nav\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"#\",\n          className: \"nav-item active\",\n          children: \"\\u9996\\u9875\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"#\",\n          className: \"nav-item\",\n          children: \"\\u6587\\u6863\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"#\",\n          className: \"nav-item\",\n          children: \"\\u5173\\u4E8E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"home-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"page-header\",\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 1,\n          className: \"page-title\",\n          children: \"\\u8BBE\\u5907\\u5DE1\\u68C0\\u7BA1\\u7406\\u7CFB\\u7EDF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n          className: \"page-subtitle\",\n          children: \"\\u672C\\u7CFB\\u7EDF\\u4E3A\\u79FB\\u52A8\\u8FD0\\u8425\\u5546\\u8BBE\\u5907\\u673A\\u623F\\u5DE1\\u68C0\\u7BA1\\u7406\\u7CFB\\u7EDF\\uFF0C\\u652F\\u6301\\u81EA\\u52A8\\u5DE1\\u68C0\\u3001\\u5F02\\u5E38\\u544A\\u8B66\\u3001\\u5468\\u671F\\u6027\\u4EFB\\u52A1\\u7BA1\\u7406\\u7B49\\u529F\\u80FD\\u3002 \\u70B9\\u51FB\\u4E0B\\u65B9\\u5361\\u7247\\u67E5\\u770B\\u5404\\u4E2A\\u529F\\u80FD\\u6A21\\u5757\\u3002\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [24, 24],\n        className: \"feature-grid\",\n        children: featureCards.map((card, index) => /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          lg: 8,\n          xl: 8,\n          children: /*#__PURE__*/_jsxDEV(FeatureCard, {\n            ...card\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"info-section\",\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 2,\n          className: \"info-title\",\n          children: \"\\u7CFB\\u7EDF\\u529F\\u80FD\\u7279\\u70B9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [24, 24],\n          className: \"info-content\",\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"info-icon\",\n                children: \"\\uD83E\\uDD16\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"info-label\",\n                children: \"\\u81EA\\u52A8\\u5DE1\\u68C0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"info-desc\",\n                children: \"\\u652F\\u6301\\u4E3B\\u673ACPU\\u3001\\u5185\\u5B58\\u3001\\u5B58\\u50A8\\u7B49\\u81EA\\u52A8\\u68C0\\u6D4B\\uFF0C\\u5F02\\u5E38\\u81EA\\u52A8\\u544A\\u8B66\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"info-icon\",\n                children: \"\\uD83D\\uDCC5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"info-label\",\n                children: \"\\u5468\\u671F\\u6027\\u4EFB\\u52A1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"info-desc\",\n                children: \"\\u652F\\u6301\\u65E5\\u3001\\u5468\\u3001\\u6708\\u3001\\u5B63\\u5EA6\\u7B49\\u4E0D\\u540C\\u9891\\u7387\\u7684\\u5DE1\\u68C0\\u914D\\u7F6E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"info-icon\",\n                children: \"\\uD83D\\uDD14\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"info-label\",\n                children: \"\\u667A\\u80FD\\u544A\\u8B66\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"info-desc\",\n                children: \"\\u6839\\u636E\\u9608\\u503C\\u81EA\\u52A8\\u63D0\\u5347\\u544A\\u8B66\\u7EA7\\u522B\\uFF0C\\u652F\\u6301\\u591A\\u79CD\\u901A\\u77E5\\u65B9\\u5F0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"info-icon\",\n                children: \"\\uD83D\\uDCCA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"info-label\",\n                children: \"\\u8F68\\u8FF9\\u8BB0\\u5F55\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"info-desc\",\n                children: \"\\u8BE6\\u7EC6\\u8BB0\\u5F55\\u5DE1\\u68C0\\u8FC7\\u7A0B\\uFF0C\\u652F\\u6301\\u8F68\\u8FF9\\u67E5\\u8BE2\\u548C\\u56DE\\u653E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 5\n  }, this);\n};\n_s2(Home, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c2 = Home;\nexport default Home;\nvar _c, _c2;\n$RefreshReg$(_c, \"FeatureCard\");\n$RefreshReg$(_c2, \"Home\");", "map": {"version": 3, "names": ["React", "useNavigate", "Card", "Row", "Col", "Typography", "<PERSON><PERSON>", "DashboardOutlined", "UnorderedListOutlined", "SearchOutlined", "SettingOutlined", "Alert<PERSON>ut<PERSON>", "jsxDEV", "_jsxDEV", "Title", "Paragraph", "FeatureCard", "icon", "title", "description", "features", "path", "color", "_s", "navigate", "hoverable", "className", "style", "height", "onClick", "children", "background", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "level", "map", "feature", "index", "type", "block", "_c", "Home", "_s2", "featureCards", "href", "gutter", "card", "xs", "sm", "lg", "xl", "md", "_c2", "$RefreshReg$"], "sources": ["D:/Documents/浩鲸/ai培训/AI赋能资料(2)/任务3-作业计划巡检/inspection-system/frontend/src/pages/Home.tsx"], "sourcesContent": ["import React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Card, Row, Col, Typography, Space, Button } from 'antd';\nimport {\n  DashboardOutlined,\n  UnorderedListOutlined,\n  SearchOutlined,\n  SettingOutlined,\n  AlertOutlined\n} from '@ant-design/icons';\n\nconst { Title, Paragraph } = Typography;\n\ninterface FeatureCardProps {\n  icon: React.ReactNode;\n  title: string;\n  description: string;\n  features: string[];\n  path: string;\n  color: string;\n}\n\nconst FeatureCard: React.FC<FeatureCardProps> = ({ icon, title, description, features, path, color }) => {\n  const navigate = useNavigate();\n\n  return (\n    <Card\n      hoverable\n      className=\"feature-card\"\n      style={{ height: '100%' }}\n      onClick={() => navigate(path)}\n    >\n      <div className=\"card-header\" style={{ background: color }}>\n        <div className=\"card-icon\">{icon}</div>\n      </div>\n      <div className=\"card-content\">\n        <Title level={4} className=\"card-title\">{title}</Title>\n        <Paragraph className=\"card-description\">{description}</Paragraph>\n        <ul className=\"card-features\">\n          {features.map((feature, index) => (\n            <li key={index}>{feature}</li>\n          ))}\n        </ul>\n        <Button type=\"primary\" className=\"modern-button\" block>\n          查看详情\n        </Button>\n      </div>\n    </Card>\n  );\n};\n\nconst Home: React.FC = () => {\n  const navigate = useNavigate();\n  \n  const featureCards: FeatureCardProps[] = [\n    {\n      icon: <DashboardOutlined />,\n      title: '仪表盘',\n      description: '系统概览页面，展示关键统计信息和实时数据',\n      features: ['统计卡片展示', '图表可视化', '最近任务列表', '告警信息展示'],\n      path: '/dashboard',\n      color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'\n    },\n    {\n      icon: <UnorderedListOutlined />,\n      title: '巡检任务',\n      description: '巡检任务管理页面，支持任务创建、执行和监控',\n      features: ['任务列表展示', '多条件筛选', '状态管理', '批量操作'],\n      path: '/tasks',\n      color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'\n    },\n    {\n      icon: <SearchOutlined />,\n      title: '任务详情',\n      description: '任务详细信息页面，包含巡检结果和轨迹记录',\n      features: ['任务基本信息', '巡检结果详情', '轨迹时间线', '相关告警'],\n      path: '/tasks',\n      color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'\n    },\n    {\n      icon: <SettingOutlined />,\n      title: '配置管理',\n      description: '巡检配置管理页面，支持配置创建和调度设置',\n      features: ['配置列表管理', '启用/禁用开关', '新建配置', '频率设置'],\n      path: '/config',\n      color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'\n    },\n    {\n      icon: <AlertOutlined />,\n      title: '告警工单',\n      description: '告警工单管理页面，处理异常告警和工单流程',\n      features: ['告警统计', '告警列表', '处理状态', '告警升级'],\n      path: '/alerts',\n      color: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)'\n    }\n  ];\n\n  return (\n    <div className=\"home-page\">\n      {/* 独立的顶部导航栏 */}\n      <div className=\"home-header\">\n        <div className=\"home-logo\">设备巡检管理系统</div>\n        <nav className=\"home-nav\">\n          <a href=\"#\" className=\"nav-item active\">首页</a>\n          <a href=\"#\" className=\"nav-item\">文档</a>\n          <a href=\"#\" className=\"nav-item\">关于</a>\n        </nav>\n      </div>\n\n      <div className=\"home-container\">\n        <div className=\"page-header\">\n          <Title level={1} className=\"page-title\">设备巡检管理系统</Title>\n          <Paragraph className=\"page-subtitle\">\n            本系统为移动运营商设备机房巡检管理系统，支持自动巡检、异常告警、周期性任务管理等功能。\n            点击下方卡片查看各个功能模块。\n          </Paragraph>\n        </div>\n\n        <Row gutter={[24, 24]} className=\"feature-grid\">\n          {featureCards.map((card, index) => (\n            <Col xs={24} sm={12} lg={8} xl={8} key={index}>\n              <FeatureCard {...card} />\n            </Col>\n          ))}\n        </Row>\n\n        <div className=\"info-section\">\n          <Title level={2} className=\"info-title\">系统功能特点</Title>\n          <Row gutter={[24, 24]} className=\"info-content\">\n            <Col xs={24} sm={12} md={6}>\n              <div className=\"info-item\">\n                <div className=\"info-icon\">🤖</div>\n                <div className=\"info-label\">自动巡检</div>\n                <div className=\"info-desc\">支持主机CPU、内存、存储等自动检测，异常自动告警</div>\n              </div>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <div className=\"info-item\">\n                <div className=\"info-icon\">📅</div>\n                <div className=\"info-label\">周期性任务</div>\n                <div className=\"info-desc\">支持日、周、月、季度等不同频率的巡检配置</div>\n              </div>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <div className=\"info-item\">\n                <div className=\"info-icon\">🔔</div>\n                <div className=\"info-label\">智能告警</div>\n                <div className=\"info-desc\">根据阈值自动提升告警级别，支持多种通知方式</div>\n              </div>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <div className=\"info-item\">\n                <div className=\"info-icon\">📊</div>\n                <div className=\"info-label\">轨迹记录</div>\n                <div className=\"info-desc\">详细记录巡检过程，支持轨迹查询和回放</div>\n              </div>\n            </Col>\n          </Row>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Home;"], "mappings": ";;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,UAAU,EAASC,MAAM,QAAQ,MAAM;AAChE,SACEC,iBAAiB,EACjBC,qBAAqB,EACrBC,cAAc,EACdC,eAAe,EACfC,aAAa,QACR,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAM;EAAEC,KAAK;EAAEC;AAAU,CAAC,GAAGV,UAAU;AAWvC,MAAMW,WAAuC,GAAGA,CAAC;EAAEC,IAAI;EAAEC,KAAK;EAAEC,WAAW;EAAEC,QAAQ;EAAEC,IAAI;EAAEC;AAAM,CAAC,KAAK;EAAAC,EAAA;EACvG,MAAMC,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAE9B,oBACEY,OAAA,CAACX,IAAI;IACHuB,SAAS;IACTC,SAAS,EAAC,cAAc;IACxBC,KAAK,EAAE;MAAEC,MAAM,EAAE;IAAO,CAAE;IAC1BC,OAAO,EAAEA,CAAA,KAAML,QAAQ,CAACH,IAAI,CAAE;IAAAS,QAAA,gBAE9BjB,OAAA;MAAKa,SAAS,EAAC,aAAa;MAACC,KAAK,EAAE;QAAEI,UAAU,EAAET;MAAM,CAAE;MAAAQ,QAAA,eACxDjB,OAAA;QAAKa,SAAS,EAAC,WAAW;QAAAI,QAAA,EAAEb;MAAI;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC,eACNtB,OAAA;MAAKa,SAAS,EAAC,cAAc;MAAAI,QAAA,gBAC3BjB,OAAA,CAACC,KAAK;QAACsB,KAAK,EAAE,CAAE;QAACV,SAAS,EAAC,YAAY;QAAAI,QAAA,EAAEZ;MAAK;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACvDtB,OAAA,CAACE,SAAS;QAACW,SAAS,EAAC,kBAAkB;QAAAI,QAAA,EAAEX;MAAW;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACjEtB,OAAA;QAAIa,SAAS,EAAC,eAAe;QAAAI,QAAA,EAC1BV,QAAQ,CAACiB,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC3B1B,OAAA;UAAAiB,QAAA,EAAiBQ;QAAO,GAAfC,KAAK;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAC9B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACLtB,OAAA,CAACP,MAAM;QAACkC,IAAI,EAAC,SAAS;QAACd,SAAS,EAAC,eAAe;QAACe,KAAK;QAAAX,QAAA,EAAC;MAEvD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX,CAAC;AAACZ,EAAA,CA3BIP,WAAuC;EAAA,QAC1Bf,WAAW;AAAA;AAAAyC,EAAA,GADxB1B,WAAuC;AA6B7C,MAAM2B,IAAc,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC3B,MAAMpB,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAE9B,MAAM4C,YAAgC,GAAG,CACvC;IACE5B,IAAI,eAAEJ,OAAA,CAACN,iBAAiB;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3BjB,KAAK,EAAE,KAAK;IACZC,WAAW,EAAE,sBAAsB;IACnCC,QAAQ,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC;IACjDC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE;EACT,CAAC,EACD;IACEL,IAAI,eAAEJ,OAAA,CAACL,qBAAqB;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC/BjB,KAAK,EAAE,MAAM;IACbC,WAAW,EAAE,uBAAuB;IACpCC,QAAQ,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC;IAC7CC,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE;EACT,CAAC,EACD;IACEL,IAAI,eAAEJ,OAAA,CAACJ,cAAc;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBjB,KAAK,EAAE,MAAM;IACbC,WAAW,EAAE,sBAAsB;IACnCC,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC;IAC/CC,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE;EACT,CAAC,EACD;IACEL,IAAI,eAAEJ,OAAA,CAACH,eAAe;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBjB,KAAK,EAAE,MAAM;IACbC,WAAW,EAAE,sBAAsB;IACnCC,QAAQ,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC;IAC/CC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE;EACT,CAAC,EACD;IACEL,IAAI,eAAEJ,OAAA,CAACF,aAAa;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBjB,KAAK,EAAE,MAAM;IACbC,WAAW,EAAE,sBAAsB;IACnCC,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IAC1CC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE;EACT,CAAC,CACF;EAED,oBACET,OAAA;IAAKa,SAAS,EAAC,WAAW;IAAAI,QAAA,gBAExBjB,OAAA;MAAKa,SAAS,EAAC,aAAa;MAAAI,QAAA,gBAC1BjB,OAAA;QAAKa,SAAS,EAAC,WAAW;QAAAI,QAAA,EAAC;MAAQ;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACzCtB,OAAA;QAAKa,SAAS,EAAC,UAAU;QAAAI,QAAA,gBACvBjB,OAAA;UAAGiC,IAAI,EAAC,GAAG;UAACpB,SAAS,EAAC,iBAAiB;UAAAI,QAAA,EAAC;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC9CtB,OAAA;UAAGiC,IAAI,EAAC,GAAG;UAACpB,SAAS,EAAC,UAAU;UAAAI,QAAA,EAAC;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACvCtB,OAAA;UAAGiC,IAAI,EAAC,GAAG;UAACpB,SAAS,EAAC,UAAU;UAAAI,QAAA,EAAC;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENtB,OAAA;MAAKa,SAAS,EAAC,gBAAgB;MAAAI,QAAA,gBAC7BjB,OAAA;QAAKa,SAAS,EAAC,aAAa;QAAAI,QAAA,gBAC1BjB,OAAA,CAACC,KAAK;UAACsB,KAAK,EAAE,CAAE;UAACV,SAAS,EAAC,YAAY;UAAAI,QAAA,EAAC;QAAQ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxDtB,OAAA,CAACE,SAAS;UAACW,SAAS,EAAC,eAAe;UAAAI,QAAA,EAAC;QAGrC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eAENtB,OAAA,CAACV,GAAG;QAAC4C,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAACrB,SAAS,EAAC,cAAc;QAAAI,QAAA,EAC5Ce,YAAY,CAACR,GAAG,CAAC,CAACW,IAAI,EAAET,KAAK,kBAC5B1B,OAAA,CAACT,GAAG;UAAC6C,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAtB,QAAA,eAChCjB,OAAA,CAACG,WAAW;YAAA,GAAKgC;UAAI;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC,GADaI,KAAK;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAExC,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENtB,OAAA;QAAKa,SAAS,EAAC,cAAc;QAAAI,QAAA,gBAC3BjB,OAAA,CAACC,KAAK;UAACsB,KAAK,EAAE,CAAE;UAACV,SAAS,EAAC,YAAY;UAAAI,QAAA,EAAC;QAAM;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACtDtB,OAAA,CAACV,GAAG;UAAC4C,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAACrB,SAAS,EAAC,cAAc;UAAAI,QAAA,gBAC7CjB,OAAA,CAACT,GAAG;YAAC6C,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACG,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACzBjB,OAAA;cAAKa,SAAS,EAAC,WAAW;cAAAI,QAAA,gBACxBjB,OAAA;gBAAKa,SAAS,EAAC,WAAW;gBAAAI,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACnCtB,OAAA;gBAAKa,SAAS,EAAC,YAAY;gBAAAI,QAAA,EAAC;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtCtB,OAAA;gBAAKa,SAAS,EAAC,WAAW;gBAAAI,QAAA,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNtB,OAAA,CAACT,GAAG;YAAC6C,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACG,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACzBjB,OAAA;cAAKa,SAAS,EAAC,WAAW;cAAAI,QAAA,gBACxBjB,OAAA;gBAAKa,SAAS,EAAC,WAAW;gBAAAI,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACnCtB,OAAA;gBAAKa,SAAS,EAAC,YAAY;gBAAAI,QAAA,EAAC;cAAK;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvCtB,OAAA;gBAAKa,SAAS,EAAC,WAAW;gBAAAI,QAAA,EAAC;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNtB,OAAA,CAACT,GAAG;YAAC6C,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACG,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACzBjB,OAAA;cAAKa,SAAS,EAAC,WAAW;cAAAI,QAAA,gBACxBjB,OAAA;gBAAKa,SAAS,EAAC,WAAW;gBAAAI,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACnCtB,OAAA;gBAAKa,SAAS,EAAC,YAAY;gBAAAI,QAAA,EAAC;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtCtB,OAAA;gBAAKa,SAAS,EAAC,WAAW;gBAAAI,QAAA,EAAC;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNtB,OAAA,CAACT,GAAG;YAAC6C,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACG,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACzBjB,OAAA;cAAKa,SAAS,EAAC,WAAW;cAAAI,QAAA,gBACxBjB,OAAA;gBAAKa,SAAS,EAAC,WAAW;gBAAAI,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACnCtB,OAAA;gBAAKa,SAAS,EAAC,YAAY;gBAAAI,QAAA,EAAC;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtCtB,OAAA;gBAAKa,SAAS,EAAC,WAAW;gBAAAI,QAAA,EAAC;cAAkB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACS,GAAA,CA/GID,IAAc;EAAA,QACD1C,WAAW;AAAA;AAAAqD,GAAA,GADxBX,IAAc;AAiHpB,eAAeA,IAAI;AAAC,IAAAD,EAAA,EAAAY,GAAA;AAAAC,YAAA,CAAAb,EAAA;AAAAa,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}