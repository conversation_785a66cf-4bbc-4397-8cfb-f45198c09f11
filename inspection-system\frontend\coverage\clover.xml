<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1754236818516" clover="3.2.0">
  <project timestamp="1754236818516" name="All files">
    <metrics statements="674" coveredstatements="30" conditionals="244" coveredconditionals="1" methods="216" coveredmethods="16" elements="1134" coveredelements="47" complexity="0" loc="674" ncloc="674" packages="6" files="11" classes="11"/>
    <package name="src">
      <metrics statements="2" coveredstatements="2" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="1"/>
      <file name="App.tsx" path="D:\Documents\浩鲸\ai培训\AI赋能资料(2)\任务3-作业计划巡检\inspection-system\frontend\src\App.tsx">
        <metrics statements="2" coveredstatements="2" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="1"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="3" type="stmt"/>
      </file>
    </package>
    <package name="src.components">
      <metrics statements="12" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="4" coveredmethods="0"/>
      <file name="Layout.tsx" path="D:\Documents\浩鲸\ai培训\AI赋能资料(2)\任务3-作业计划巡检\inspection-system\frontend\src\components\Layout.tsx">
        <metrics statements="12" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="14" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.pages">
      <metrics statements="589" coveredstatements="9" conditionals="214" coveredconditionals="0" methods="169" coveredmethods="3"/>
      <file name="AlertManagement.tsx" path="D:\Documents\浩鲸\ai培训\AI赋能资料(2)\任务3-作业计划巡检\inspection-system\frontend\src\pages\AlertManagement.tsx">
        <metrics statements="162" coveredstatements="0" conditionals="43" coveredconditionals="0" methods="47" coveredmethods="0"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="90" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="93" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="103" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="210" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="239" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="245" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="248" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="271" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="272" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="274" count="0" type="stmt"/>
        <line num="275" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="313" count="0" type="stmt"/>
        <line num="319" count="0" type="stmt"/>
        <line num="325" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="326" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="327" count="0" type="stmt"/>
        <line num="335" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="342" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="349" count="0" type="stmt"/>
        <line num="356" count="0" type="stmt"/>
        <line num="364" count="0" type="stmt"/>
        <line num="376" count="0" type="stmt"/>
        <line num="387" count="0" type="stmt"/>
        <line num="398" count="0" type="stmt"/>
        <line num="407" count="0" type="stmt"/>
        <line num="426" count="0" type="stmt"/>
        <line num="429" count="0" type="stmt"/>
        <line num="434" count="0" type="stmt"/>
        <line num="435" count="0" type="stmt"/>
        <line num="436" count="0" type="stmt"/>
        <line num="437" count="0" type="stmt"/>
        <line num="438" count="0" type="stmt"/>
        <line num="440" count="0" type="stmt"/>
        <line num="443" count="0" type="stmt"/>
        <line num="445" count="0" type="stmt"/>
        <line num="453" count="0" type="stmt"/>
        <line num="571" count="0" type="stmt"/>
        <line num="574" count="0" type="stmt"/>
        <line num="577" count="0" type="stmt"/>
        <line num="595" count="0" type="stmt"/>
        <line num="608" count="0" type="stmt"/>
        <line num="609" count="0" type="stmt"/>
        <line num="632" count="0" type="stmt"/>
      </file>
      <file name="ConfigManagement.tsx" path="D:\Documents\浩鲸\ai培训\AI赋能资料(2)\任务3-作业计划巡检\inspection-system\frontend\src\pages\ConfigManagement.tsx">
        <metrics statements="117" coveredstatements="0" conditionals="49" coveredconditionals="0" methods="33" coveredmethods="0"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="74" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="107" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="110" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="121" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="124" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="135" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="138" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="151" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="152" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="157" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="158" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="163" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="166" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="199" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="204" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="207" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="245" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="262" count="0" type="stmt"/>
        <line num="268" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="283" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="324" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="333" count="0" type="stmt"/>
        <line num="337" count="0" type="stmt"/>
        <line num="344" count="0" type="stmt"/>
        <line num="362" count="0" type="stmt"/>
        <line num="370" count="0" type="stmt"/>
        <line num="419" count="0" type="stmt"/>
        <line num="426" count="0" type="stmt"/>
        <line num="446" count="0" type="stmt"/>
        <line num="457" count="0" type="stmt"/>
      </file>
      <file name="Dashboard.tsx" path="D:\Documents\浩鲸\ai培训\AI赋能资料(2)\任务3-作业计划巡检\inspection-system\frontend\src\pages\Dashboard.tsx">
        <metrics statements="42" coveredstatements="0" conditionals="20" coveredconditionals="0" methods="12" coveredmethods="0"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="28" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="45" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="80" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="228" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="229" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
      </file>
      <file name="Home.tsx" path="D:\Documents\浩鲸\ai培训\AI赋能资料(2)\任务3-作业计划巡检\inspection-system\frontend\src\pages\Home.tsx">
        <metrics statements="17" coveredstatements="8" conditionals="0" coveredconditionals="0" methods="12" coveredmethods="3"/>
        <line num="12" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="53" count="3" type="stmt"/>
        <line num="55" count="3" type="stmt"/>
        <line num="98" count="3" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="164" count="15" type="stmt"/>
        <line num="175" count="60" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
      </file>
      <file name="TaskDetail.tsx" path="D:\Documents\浩鲸\ai培训\AI赋能资料(2)\任务3-作业计划巡检\inspection-system\frontend\src\pages\TaskDetail.tsx">
        <metrics statements="108" coveredstatements="1" conditionals="74" coveredconditionals="0" methods="24" coveredmethods="0"/>
        <line num="32" count="1" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="51" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="68" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="93" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="145" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="146" count="0" type="stmt"/>
        <line num="153" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="154" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="177" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="178" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="188" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="189" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="190" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="304" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="305" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="306" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="307" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
        <line num="316" count="0" type="stmt"/>
        <line num="353" count="0" type="stmt"/>
        <line num="436" count="0" type="stmt"/>
        <line num="467" count="0" type="stmt"/>
        <line num="511" count="0" type="stmt"/>
      </file>
      <file name="TaskList.tsx" path="D:\Documents\浩鲸\ai培训\AI赋能资料(2)\任务3-作业计划巡检\inspection-system\frontend\src\pages\TaskList.tsx">
        <metrics statements="143" coveredstatements="0" conditionals="28" coveredconditionals="0" methods="41" coveredmethods="0"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="70" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="207" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="209" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="233" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="234" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="239" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="242" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="270" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="279" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="291" count="0" type="stmt"/>
        <line num="300" count="0" type="stmt"/>
        <line num="305" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="331" count="0" type="stmt"/>
        <line num="336" count="0" type="stmt"/>
        <line num="346" count="0" type="stmt"/>
        <line num="358" count="0" type="stmt"/>
        <line num="366" count="0" type="stmt"/>
        <line num="378" count="0" type="stmt"/>
        <line num="387" count="0" type="stmt"/>
        <line num="406" count="0" type="stmt"/>
        <line num="409" count="0" type="stmt"/>
        <line num="413" count="0" type="stmt"/>
        <line num="421" count="0" type="stmt"/>
        <line num="495" count="0" type="stmt"/>
        <line num="498" count="0" type="stmt"/>
        <line num="501" count="0" type="stmt"/>
        <line num="519" count="0" type="stmt"/>
        <line num="532" count="0" type="stmt"/>
        <line num="563" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.services">
      <metrics statements="42" coveredstatements="16" conditionals="0" coveredconditionals="0" methods="38" coveredmethods="12"/>
      <file name="api.ts" path="D:\Documents\浩鲸\ai培训\AI赋能资料(2)\任务3-作业计划巡检\inspection-system\frontend\src\services\api.ts">
        <metrics statements="42" coveredstatements="16" conditionals="0" coveredconditionals="0" methods="38" coveredmethods="12"/>
        <line num="15" count="2" type="stmt"/>
        <line num="18" count="2" type="stmt"/>
        <line num="23" count="2" type="stmt"/>
        <line num="26" count="2" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="36" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="46" count="1" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="93" count="2" type="stmt"/>
        <line num="96" count="1" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="106" count="1" type="stmt"/>
        <line num="111" count="1" type="stmt"/>
        <line num="116" count="1" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="148" count="2" type="stmt"/>
        <line num="151" count="1" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="166" count="1" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.types">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="index.ts" path="D:\Documents\浩鲸\ai培训\AI赋能资料(2)\任务3-作业计划巡检\inspection-system\frontend\src\types\index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.utils">
      <metrics statements="29" coveredstatements="3" conditionals="24" coveredconditionals="1" methods="4" coveredmethods="0"/>
      <file name="request.ts" path="D:\Documents\浩鲸\ai培训\AI赋能资料(2)\任务3-作业计划巡检\inspection-system\frontend\src\utils\request.ts">
        <metrics statements="29" coveredstatements="3" conditionals="24" coveredconditionals="1" methods="4" coveredmethods="0"/>
        <line num="6" count="3" type="stmt"/>
        <line num="15" count="3" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="26" count="3" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="31" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="32" count="0" type="stmt"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="39" count="0" type="stmt"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="47" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="stmt"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="64" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
