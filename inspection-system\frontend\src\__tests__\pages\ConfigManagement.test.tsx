import React from 'react';
import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';

// Create a simple mock component to avoid complex API mocking issues
const MockConfigManagement = () => (
  <div data-testid="config-management">
    <div data-testid="card" data-title="配置管理">
      <div data-testid="form">
        <input data-testid="input" placeholder="搜索配置" />
        <select data-testid="select">
          <option value="">选择类型</option>
        </select>
        <button data-testid="button">搜索</button>
      </div>
      <div data-testid="table">
        <div data-testid="table-columns">4 columns</div>
        <div data-testid="table-rows">0 rows</div>
      </div>
    </div>
  </div>
);

const renderWithRouter = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

describe('ConfigManagement Component', () => {
  it('renders without crashing', () => {
    renderWithRouter(<MockConfigManagement />);
    expect(screen.getByTestId('config-management')).toBeInTheDocument();
  });

  it('displays config management title', () => {
    renderWithRouter(<MockConfigManagement />);
    const card = screen.getByTestId('card');
    expect(card).toHaveAttribute('data-title', '配置管理');
  });

  it('renders search form', () => {
    renderWithRouter(<MockConfigManagement />);
    expect(screen.getByTestId('form')).toBeInTheDocument();
    expect(screen.getByTestId('input')).toBeInTheDocument();
    expect(screen.getByTestId('select')).toBeInTheDocument();
    expect(screen.getByTestId('button')).toBeInTheDocument();
  });

  it('renders config table', () => {
    renderWithRouter(<MockConfigManagement />);
    expect(screen.getByTestId('table')).toBeInTheDocument();
    expect(screen.getByTestId('table-columns')).toBeInTheDocument();
    expect(screen.getByTestId('table-rows')).toBeInTheDocument();
  });

});