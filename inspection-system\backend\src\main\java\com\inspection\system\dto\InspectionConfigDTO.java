package com.inspection.system.dto;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;

import java.time.LocalDateTime;

/**
 * 巡检配置DTO
 */
@Data
public class InspectionConfigDTO {
    
    /**
     * 配置ID
     */
    private Long id;
    
    /**
     * 配置名称
     */
    @NotBlank(message = "配置名称不能为空")
    private String configName;
    
    /**
     * 巡检目标类型：DEVICE-设备，ROOM-机房
     */
    @NotBlank(message = "目标类型不能为空")
    private String targetType;
    
    /**
     * 巡检目标ID
     */
    @NotNull(message = "目标ID不能为空")
    private Long targetId;
    
    /**
     * 目标名称（冗余字段，用于显示）
     */
    private String targetName;
    
    /**
     * 巡检频率：DAILY-每日，WEEKLY-每周，MONTHLY-每月，QUARTERLY-每季度
     */
    @NotBlank(message = "频率类型不能为空")
    private String frequencyType;
    
    /**
     * 频率值（如：1表示每天1次，2表示每周2次）
     */
    @NotNull(message = "频率值不能为空")
    @Min(value = 1, message = "频率值不能小于1")
    @Max(value = 10, message = "频率值不能大于10")
    private Integer frequencyValue;
    
    /**
     * 巡检项目JSON
     */
    private String inspectionItems;
    
    /**
     * 是否自动巡检：false-否，true-是
     */
    private Boolean autoInspection = false;
    
    /**
     * 状态：ACTIVE-启用，INACTIVE-停用
     */
    private String status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;
    
    /**
     * Cron表达式（根据频率类型和频率值生成）
     */
    private String cronExpression;
    
    /**
     * 下次执行时间
     */
    private LocalDateTime nextExecuteTime;
    
    /**
     * 最后执行时间
     */
    private LocalDateTime lastExecuteTime;
    
    /**
     * 执行次数
     */
    private Integer executeCount = 0;

    // 手动添加getter方法以解决编译问题
    public String getStatus() {
        return status;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public Integer getFrequencyValue() {
        return frequencyValue;
    }

    public String getInspectionItems() {
        return inspectionItems;
    }

    public Boolean getAutoInspection() {
        return autoInspection;
    }

    public Long getTargetId() {
        return targetId;
    }

    public String getFrequencyType() {
        return frequencyType;
    }

    public String getConfigName() {
        return configName;
    }

    public String getTargetType() {
        return targetType;
    }

    public Long getId() {
        return id;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public void setInspectionItems(String inspectionItems) {
        this.inspectionItems = inspectionItems;
    }

    public void setAutoInspection(Boolean autoInspection) {
        this.autoInspection = autoInspection;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public void setTargetType(String targetType) {
        this.targetType = targetType;
    }

    public void setTargetId(Long targetId) {
        this.targetId = targetId;
    }

    public void setFrequencyType(String frequencyType) {
        this.frequencyType = frequencyType;
    }

    public void setFrequencyValue(Integer frequencyValue) {
        this.frequencyValue = frequencyValue;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTargetName() {
        return targetName;
    }

    public void setTargetName(String targetName) {
        this.targetName = targetName;
    }

    public String getCronExpression() {
        return cronExpression;
    }

    public void setCronExpression(String cronExpression) {
        this.cronExpression = cronExpression;
    }

    public LocalDateTime getNextExecuteTime() {
        return nextExecuteTime;
    }

    public void setNextExecuteTime(LocalDateTime nextExecuteTime) {
        this.nextExecuteTime = nextExecuteTime;
    }

    public LocalDateTime getLastExecuteTime() {
        return lastExecuteTime;
    }

    public void setLastExecuteTime(LocalDateTime lastExecuteTime) {
        this.lastExecuteTime = lastExecuteTime;
    }

    public Integer getExecuteCount() {
        return executeCount;
    }

    public void setExecuteCount(Integer executeCount) {
        this.executeCount = executeCount;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
}