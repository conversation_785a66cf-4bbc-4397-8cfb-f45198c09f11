{"ast": null, "code": "import axios from 'axios';\nimport { message } from 'antd';\n// 创建axios实例\nconst request = axios.create({\n  baseURL: process.env.REACT_APP_API_BASE_URL || 'http://localhost:8003/api',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// 请求拦截器\nrequest.interceptors.request.use(config => {\n  // 可以在这里添加token等认证信息\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// 响应拦截器\nrequest.interceptors.response.use(response => {\n  const {\n    data\n  } = response;\n\n  // 检查业务状态码\n  if (data.success) {\n    return response;\n  } else {\n    message.error(data.message || '请求失败');\n    return Promise.reject(new Error(data.message || '请求失败'));\n  }\n}, error => {\n  let errorMessage = '网络错误';\n  if (error.response) {\n    const {\n      status,\n      data\n    } = error.response;\n    switch (status) {\n      case 400:\n        errorMessage = (data === null || data === void 0 ? void 0 : data.message) || '请求参数错误';\n        break;\n      case 401:\n        errorMessage = '未授权，请重新登录';\n        break;\n      case 403:\n        errorMessage = '拒绝访问';\n        break;\n      case 404:\n        errorMessage = '请求资源不存在';\n        break;\n      case 500:\n        errorMessage = (data === null || data === void 0 ? void 0 : data.message) || '服务器内部错误';\n        break;\n      default:\n        errorMessage = (data === null || data === void 0 ? void 0 : data.message) || `请求失败 (${status})`;\n    }\n  } else if (error.request) {\n    errorMessage = '网络连接失败';\n  }\n  message.error(errorMessage);\n  return Promise.reject(error);\n});\nexport default request;", "map": {"version": 3, "names": ["axios", "message", "request", "create", "baseURL", "process", "env", "REACT_APP_API_BASE_URL", "timeout", "headers", "interceptors", "use", "config", "error", "Promise", "reject", "response", "data", "success", "Error", "errorMessage", "status"], "sources": ["D:/Documents/浩鲸/ai培训/AI赋能资料(2)/任务3-作业计划巡检/inspection-system/frontend/src/utils/request.ts"], "sourcesContent": ["import axios, { AxiosResponse, AxiosError } from 'axios';\nimport { message } from 'antd';\nimport { ApiResponse } from '../types';\n\n// 创建axios实例\nconst request = axios.create({\n  baseURL: process.env.REACT_APP_API_BASE_URL || 'http://localhost:8003/api',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// 请求拦截器\nrequest.interceptors.request.use(\n  (config) => {\n    // 可以在这里添加token等认证信息\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器\nrequest.interceptors.response.use(\n  (response: AxiosResponse<ApiResponse>) => {\n    const { data } = response;\n    \n    // 检查业务状态码\n    if (data.success) {\n      return response;\n    } else {\n      message.error(data.message || '请求失败');\n      return Promise.reject(new Error(data.message || '请求失败'));\n    }\n  },\n  (error: AxiosError<ApiResponse>) => {\n    let errorMessage = '网络错误';\n    \n    if (error.response) {\n      const { status, data } = error.response;\n      \n      switch (status) {\n        case 400:\n          errorMessage = data?.message || '请求参数错误';\n          break;\n        case 401:\n          errorMessage = '未授权，请重新登录';\n          break;\n        case 403:\n          errorMessage = '拒绝访问';\n          break;\n        case 404:\n          errorMessage = '请求资源不存在';\n          break;\n        case 500:\n          errorMessage = data?.message || '服务器内部错误';\n          break;\n        default:\n          errorMessage = data?.message || `请求失败 (${status})`;\n      }\n    } else if (error.request) {\n      errorMessage = '网络连接失败';\n    }\n    \n    message.error(errorMessage);\n    return Promise.reject(error);\n  }\n);\n\nexport default request;"], "mappings": "AAAA,OAAOA,KAAK,MAAqC,OAAO;AACxD,SAASC,OAAO,QAAQ,MAAM;AAG9B;AACA,MAAMC,OAAO,GAAGF,KAAK,CAACG,MAAM,CAAC;EAC3BC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,2BAA2B;EAC1EC,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAP,OAAO,CAACQ,YAAY,CAACR,OAAO,CAACS,GAAG,CAC7BC,MAAM,IAAK;EACV;EACA,OAAOA,MAAM;AACf,CAAC,EACAC,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAX,OAAO,CAACQ,YAAY,CAACM,QAAQ,CAACL,GAAG,CAC9BK,QAAoC,IAAK;EACxC,MAAM;IAAEC;EAAK,CAAC,GAAGD,QAAQ;;EAEzB;EACA,IAAIC,IAAI,CAACC,OAAO,EAAE;IAChB,OAAOF,QAAQ;EACjB,CAAC,MAAM;IACLf,OAAO,CAACY,KAAK,CAACI,IAAI,CAAChB,OAAO,IAAI,MAAM,CAAC;IACrC,OAAOa,OAAO,CAACC,MAAM,CAAC,IAAII,KAAK,CAACF,IAAI,CAAChB,OAAO,IAAI,MAAM,CAAC,CAAC;EAC1D;AACF,CAAC,EACAY,KAA8B,IAAK;EAClC,IAAIO,YAAY,GAAG,MAAM;EAEzB,IAAIP,KAAK,CAACG,QAAQ,EAAE;IAClB,MAAM;MAAEK,MAAM;MAAEJ;IAAK,CAAC,GAAGJ,KAAK,CAACG,QAAQ;IAEvC,QAAQK,MAAM;MACZ,KAAK,GAAG;QACND,YAAY,GAAG,CAAAH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEhB,OAAO,KAAI,QAAQ;QACxC;MACF,KAAK,GAAG;QACNmB,YAAY,GAAG,WAAW;QAC1B;MACF,KAAK,GAAG;QACNA,YAAY,GAAG,MAAM;QACrB;MACF,KAAK,GAAG;QACNA,YAAY,GAAG,SAAS;QACxB;MACF,KAAK,GAAG;QACNA,YAAY,GAAG,CAAAH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEhB,OAAO,KAAI,SAAS;QACzC;MACF;QACEmB,YAAY,GAAG,CAAAH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEhB,OAAO,KAAI,SAASoB,MAAM,GAAG;IACtD;EACF,CAAC,MAAM,IAAIR,KAAK,CAACX,OAAO,EAAE;IACxBkB,YAAY,GAAG,QAAQ;EACzB;EAEAnB,OAAO,CAACY,KAAK,CAACO,YAAY,CAAC;EAC3B,OAAON,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,eAAeX,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}