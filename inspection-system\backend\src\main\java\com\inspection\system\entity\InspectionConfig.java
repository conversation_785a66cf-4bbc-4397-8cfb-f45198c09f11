package com.inspection.system.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

/**
 * 巡检任务配置实体类
 */
@Data
@Entity
@Table(name = "inspection_config")
@EqualsAndHashCode(callSuper = false)
public class InspectionConfig {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 配置名称
     */
    @Column(name = "config_name", nullable = false, length = 100)
    private String configName;

    /**
     * 巡检目标类型：DEVICE-设备，ROOM-机房
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "target_type", nullable = false, length = 20)
    private TargetType targetType;

    /**
     * 巡检目标ID
     */
    @Column(name = "target_id", nullable = false)
    private Long targetId;

    /**
     * 巡检频率：DAILY-每日，WEEKLY-每周，MONTHLY-每月，QUARTERLY-每季度
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "frequency_type", nullable = false, length = 20)
    private FrequencyType frequencyType;

    /**
     * 巡检频率值
     */
    @Column(name = "frequency_value", nullable = false)
    private Integer frequencyValue;

    /**
     * Cron表达式
     */
    @Column(name = "cron_expression", length = 100)
    private String cronExpression;

    /**
     * 巡检项目配置（JSON格式）
     */
    @Column(name = "inspection_items", columnDefinition = "TEXT")
    private String inspectionItems;

    /**
     * 是否自动巡检：false-否，true-是
     */
    @Column(name = "auto_inspection")
    private Boolean autoInspection = false;

    /**
     * 状态：ACTIVE-启用，INACTIVE-停用
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", length = 20)
    private ConfigStatus status = ConfigStatus.ACTIVE;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    /**
     * 创建人
     */
    @Column(name = "created_by", length = 50)
    private String createdBy;

    /**
     * 更新人
     */
    @Column(name = "updated_by", length = 50)
    private String updatedBy;

    // 手动添加getter/setter方法以解决编译问题
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getConfigName() {
        return configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public TargetType getTargetType() {
        return targetType;
    }

    public void setTargetType(TargetType targetType) {
        this.targetType = targetType;
    }

    public Long getTargetId() {
        return targetId;
    }

    public void setTargetId(Long targetId) {
        this.targetId = targetId;
    }

    public FrequencyType getFrequencyType() {
        return frequencyType;
    }

    public void setFrequencyType(FrequencyType frequencyType) {
        this.frequencyType = frequencyType;
    }

    public Integer getFrequencyValue() {
        return frequencyValue;
    }

    public void setFrequencyValue(Integer frequencyValue) {
        this.frequencyValue = frequencyValue;
    }

    public String getCronExpression() {
        return cronExpression;
    }

    public void setCronExpression(String cronExpression) {
        this.cronExpression = cronExpression;
    }

    public String getInspectionItems() {
        return inspectionItems;
    }

    public void setInspectionItems(String inspectionItems) {
        this.inspectionItems = inspectionItems;
    }

    public Boolean getAutoInspection() {
        return autoInspection;
    }

    public void setAutoInspection(Boolean autoInspection) {
        this.autoInspection = autoInspection;
    }

    public ConfigStatus getStatus() {
        return status;
    }

    public void setStatus(ConfigStatus status) {
        this.status = status;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    /**
     * 巡检目标类型枚举
     */
    public enum TargetType {
        DEVICE, ROOM
    }

    /**
     * 频率类型枚举
     */
    public enum FrequencyType {
        DAILY, WEEKLY, MONTHLY, QUARTERLY
    }

    /**
     * 配置状态枚举
     */
    public enum ConfigStatus {
        ACTIVE, INACTIVE
    }
}