import React from 'react';
import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import App from '../App';

// Mock antd components to avoid issues in test environment
jest.mock('antd', () => ({
  ...jest.requireActual('antd'),
  ConfigProvider: ({ children }: any) => children,
}));

// Mock the Layout component
jest.mock('../components/Layout', () => {
  return function MockLayout({ children }: any) {
    return <div data-testid="layout">{children}</div>;
  };
});

// Mock all page components
jest.mock('../pages/Dashboard', () => {
  return function MockDashboard() {
    return <div data-testid="dashboard">Dashboard Page</div>;
  };
});

jest.mock('../pages/TaskList', () => {
  return function MockTaskList() {
    return <div data-testid="task-list">Task List Page</div>;
  };
});

jest.mock('../pages/ConfigManagement', () => {
  return function MockConfigManagement() {
    return <div data-testid="config-management">Config Management Page</div>;
  };
});

jest.mock('../pages/AlertManagement', () => {
  return function MockAlertManagement() {
    return <div data-testid="alert-management">Alert Management Page</div>;
  };
});

const renderWithRouter = (component: React.ReactElement) => {
  return render(component);
};

describe('App Component', () => {
  test('renders without crashing', () => {
    renderWithRouter(<App />);
    // App renders Home page by default (route "/")
    expect(screen.getByText('智能运维平台')).toBeInTheDocument();
  });

  test('renders home page by default', () => {
    renderWithRouter(<App />);
    // Check for Home page content
    expect(screen.getByText('新一代设备巡检')).toBeInTheDocument();
    expect(screen.getByText('管理系统')).toBeInTheDocument();
  });

  test('has proper routing structure', () => {
    const { container } = renderWithRouter(<App />);
    expect(container.firstChild).toBeInTheDocument();
  });
});