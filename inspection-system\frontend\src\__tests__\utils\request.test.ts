// Mock antd message
jest.mock('antd', () => ({
  message: {
    error: jest.fn(),
    success: jest.fn(),
  },
}));

// Mock axios
const mockAxiosInstance = {
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
  interceptors: {
    request: { use: jest.fn() },
    response: { use: jest.fn() },
  },
};

jest.mock('axios', () => ({
  create: jest.fn(() => mockAxiosInstance),
}));

describe('Request Utility', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('request module can be imported', () => {
    // Import request to trigger axios.create call
    const request = require('../../utils/request').default;
    expect(request).toBeDefined();
  });

  test('handles successful request', async () => {
    const mockResponse = {
      data: {
        success: true,
        message: 'success',
        data: { test: 'data' }
      },
      status: 200,
      statusText: 'OK',
    };

    mockAxiosInstance.get.mockResolvedValue(mockResponse);

    // Import request after mocking
    const request = require('../../utils/request').default;

    const result = await request.get('/test');

    expect(result).toEqual(mockResponse);
    expect(mockAxiosInstance.get).toHaveBeenCalledWith('/test');
  });

  test('handles request with data', async () => {
    const mockResponse = {
      data: {
        success: true,
        data: { id: 1 }
      },
      status: 201,
      statusText: 'Created',
    };

    const requestData = { name: 'test' };
    mockAxiosInstance.post.mockResolvedValue(mockResponse);

    const request = require('../../utils/request').default;

    const result = await request.post('/test', requestData);

    expect(result).toEqual(mockResponse);
    expect(mockAxiosInstance.post).toHaveBeenCalledWith('/test', requestData);
  });

  test('handles request with params', async () => {
    const mockResponse = {
      data: {
        success: true,
        data: { results: [] }
      },
      status: 200,
      statusText: 'OK',
    };

    const requestParams = { page: 1, size: 10 };
    mockAxiosInstance.get.mockResolvedValue(mockResponse);

    const request = require('../../utils/request').default;

    const result = await request.get('/test', { params: requestParams });

    expect(result).toEqual(mockResponse);
    expect(mockAxiosInstance.get).toHaveBeenCalledWith('/test', { params: requestParams });
  });

  test('handles error responses', async () => {
    const errorResponse = {
      response: {
        status: 400,
        data: {
          success: false,
          message: 'Bad Request',
        },
      },
    };

    mockAxiosInstance.get.mockRejectedValue(errorResponse);
    const request = require('../../utils/request').default;

    await expect(request.get('/test')).rejects.toEqual(errorResponse);
  });

  test('handles network error', async () => {
    const networkError = new Error('Network Error');

    mockAxiosInstance.get.mockRejectedValue(networkError);
    const request = require('../../utils/request').default;

    await expect(request.get('/test')).rejects.toEqual(networkError);
  });

  test('supports different HTTP methods', async () => {
    const mockResponse = {
      data: {
        success: true,
        data: { test: true }
      },
      status: 200,
      statusText: 'OK',
    };

    mockAxiosInstance.get.mockResolvedValue(mockResponse);
    mockAxiosInstance.post.mockResolvedValue(mockResponse);
    mockAxiosInstance.put.mockResolvedValue(mockResponse);
    mockAxiosInstance.delete.mockResolvedValue(mockResponse);

    const request = require('../../utils/request').default;

    // Test different methods
    await request.get('/test');
    expect(mockAxiosInstance.get).toHaveBeenCalledWith('/test');

    await request.post('/test', { data: 'test' });
    expect(mockAxiosInstance.post).toHaveBeenCalledWith('/test', { data: 'test' });

    await request.put('/test', { data: 'test' });
    expect(mockAxiosInstance.put).toHaveBeenCalledWith('/test', { data: 'test' });

    await request.delete('/test');
    expect(mockAxiosInstance.delete).toHaveBeenCalledWith('/test');
  });
});