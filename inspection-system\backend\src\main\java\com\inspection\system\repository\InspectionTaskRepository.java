package com.inspection.system.repository;

import com.inspection.system.entity.InspectionTask;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 巡检任务数据访问层
 */
@Repository
public interface InspectionTaskRepository extends JpaRepository<InspectionTask, Long> {

    /**
     * 根据任务编码查询任务
     */
    Optional<InspectionTask> findByTaskCode(String taskCode);

    /**
     * 根据配置ID查询任务列表
     */
    List<InspectionTask> findByConfigId(Long configId);

    /**
     * 根据任务状态查询任务列表
     */
    List<InspectionTask> findByStatus(InspectionTask.TaskStatus status);

    /**
     * 根据任务类型查询任务列表
     */
    List<InspectionTask> findByTaskType(InspectionTask.TaskType taskType);

    /**
     * 根据优先级查询任务列表
     */
    List<InspectionTask> findByPriority(InspectionTask.TaskPriority priority);

    /**
     * 根据巡检人员查询任务列表
     */
    List<InspectionTask> findByInspector(String inspector);

    /**
     * 根据目标类型和目标ID查询任务列表
     */
    List<InspectionTask> findByTargetTypeAndTargetId(InspectionTask.TargetType targetType, Long targetId);

    /**
     * 查询指定时间范围内的任务
     */
    List<InspectionTask> findByScheduledTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询待执行的任务（按计划时间升序）
     */
    @Query("SELECT t FROM InspectionTask t WHERE t.status = 'PENDING' AND t.scheduledTime <= :currentTime ORDER BY t.scheduledTime ASC")
    List<InspectionTask> findPendingTasksBeforeTime(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 查询正在执行的任务
     */
    @Query("SELECT t FROM InspectionTask t WHERE t.status = 'IN_PROGRESS'")
    List<InspectionTask> findInProgressTasks();

    /**
     * 统计各状态任务数量
     */
    @Query("SELECT t.status, COUNT(t) FROM InspectionTask t GROUP BY t.status")
    List<Object[]> countByStatus();

    /**
     * 根据状态统计任务数量
     */
    @Query("SELECT COUNT(t) FROM InspectionTask t WHERE t.status = :status")
    Long countByStatus(@Param("status") String status);

    /**
     * 统计各优先级任务数量
     */
    @Query("SELECT t.priority, COUNT(t) FROM InspectionTask t GROUP BY t.priority")
    List<Object[]> countByPriority();

    /**
     * 统计各结果类型任务数量
     */
    @Query("SELECT t.result, COUNT(t) FROM InspectionTask t WHERE t.result IS NOT NULL GROUP BY t.result")
    List<Object[]> countByResult();

    /**
     * 查询最近完成的任务
     */
    @Query("SELECT t FROM InspectionTask t WHERE t.status = 'COMPLETED' ORDER BY t.endTime DESC")
    Page<InspectionTask> findRecentCompletedTasks(Pageable pageable);

    /**
     * 查询今日任务统计
     */
    @Query("SELECT COUNT(t) FROM InspectionTask t WHERE t.scheduledTime >= CURRENT_DATE")
    Long countTodayTasks();

    /**
     * 查询今日完成任务数
     */
    @Query("SELECT COUNT(t) FROM InspectionTask t WHERE t.status = 'COMPLETED' AND t.endTime >= CURRENT_DATE")
    Long countTodayCompletedTasks();

    /**
     * 查询异常任务数
     */
    @Query("SELECT COUNT(t) FROM InspectionTask t WHERE t.result = 'ABNORMAL'")
    Long countAbnormalTasks();

    /**
     * 检查任务编码是否存在
     */
    boolean existsByTaskCode(String taskCode);

    /**
     * 多条件分页查询任务
     */
    @Query("SELECT t FROM InspectionTask t WHERE " +
           "(:taskCode IS NULL OR t.taskCode LIKE %:taskCode%) AND " +
           "(:status IS NULL OR t.status = :status) AND " +
           "(:taskType IS NULL OR t.taskType = :taskType) AND " +
           "(:priority IS NULL OR t.priority = :priority) AND " +
           "(:targetType IS NULL OR t.targetType = :targetType) AND " +
           "(:inspector IS NULL OR t.inspector LIKE %:inspector%) AND " +
           "(:startTime IS NULL OR t.scheduledTime >= :startTime) AND " +
           "(:endTime IS NULL OR t.scheduledTime <= :endTime)")
    Page<InspectionTask> findByConditions(@Param("taskCode") String taskCode,
                                         @Param("status") InspectionTask.TaskStatus status,
                                         @Param("taskType") InspectionTask.TaskType taskType,
                                         @Param("priority") InspectionTask.TaskPriority priority,
                                         @Param("targetType") InspectionTask.TargetType targetType,
                                         @Param("inspector") String inspector,
                                         @Param("startTime") LocalDateTime startTime,
                                         @Param("endTime") LocalDateTime endTime,
                                         Pageable pageable);

    /**
     * 查询任务趋势数据（按日期统计）
     */
    @Query("SELECT CAST(DATE(t.scheduledTime) AS STRING) as date, COUNT(t) as count " +
           "FROM InspectionTask t " +
           "WHERE t.scheduledTime >= :startDate " +
           "GROUP BY DATE(t.scheduledTime) " +
           "ORDER BY DATE(t.scheduledTime)")
    List<Object[]> getTaskTrendData(@Param("startDate") LocalDateTime startDate);

    /**
     * 查询待执行的自动巡检任务
     */
    @Query("SELECT t FROM InspectionTask t WHERE t.status = 'PENDING' AND t.taskType = 'AUTO' AND t.scheduledTime <= :currentTime ORDER BY t.scheduledTime ASC")
    List<InspectionTask> findPendingAutoTasks(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 查询超时任务
     */
    @Query("SELECT t FROM InspectionTask t WHERE t.status = 'IN_PROGRESS' AND t.startTime <= :timeoutThreshold")
    List<InspectionTask> findTimeoutTasks(@Param("timeoutThreshold") LocalDateTime timeoutThreshold);

    /**
     * 检查指定配置和时间范围内是否存在任务
     */
    boolean existsByConfigIdAndScheduledTimeBetween(Long configId, LocalDateTime startTime, LocalDateTime endTime);
}