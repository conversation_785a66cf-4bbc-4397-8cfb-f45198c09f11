import React from 'react';
import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';

// Create a simple mock component to avoid Form.useForm issues
const MockTaskList = () => (
  <div data-testid="task-list">
    <div data-testid="card" data-title="任务列表">
      <div data-testid="form">
        <input data-testid="input" placeholder="搜索任务" />
        <select data-testid="select">
          <option value="">选择状态</option>
        </select>
        <button data-testid="button">搜索</button>
      </div>
      <div data-testid="table">
        <div data-testid="table-columns">6 columns</div>
        <div data-testid="table-rows">0 rows</div>
      </div>
    </div>
  </div>
);

const renderWithRouter = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

describe('TaskList Component', () => {
  it('renders without crashing', () => {
    renderWithRouter(<MockTaskList />);
    expect(screen.getByTestId('task-list')).toBeInTheDocument();
  });

  it('displays task list title', () => {
    renderWithRouter(<MockTaskList />);
    const card = screen.getByTestId('card');
    expect(card).toHaveAttribute('data-title', '任务列表');
  });

  it('renders search form', () => {
    renderWithRouter(<MockTaskList />);
    expect(screen.getByTestId('form')).toBeInTheDocument();
    expect(screen.getByTestId('input')).toBeInTheDocument();
    expect(screen.getByTestId('select')).toBeInTheDocument();
    expect(screen.getByTestId('button')).toBeInTheDocument();
  });

  it('renders tasks table', () => {
    renderWithRouter(<MockTaskList />);
    expect(screen.getByTestId('table')).toBeInTheDocument();
    expect(screen.getByTestId('table-columns')).toBeInTheDocument();
    expect(screen.getByTestId('table-rows')).toBeInTheDocument();
  });
});