{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents\\\\\\u6D69\\u9CB8\\\\ai\\u57F9\\u8BAD\\\\AI\\u8D4B\\u80FD\\u8D44\\u6599(2)\\\\\\u4EFB\\u52A13-\\u4F5C\\u4E1A\\u8BA1\\u5212\\u5DE1\\u68C0\\\\inspection-system\\\\frontend\\\\src\\\\App.tsx\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { ConfigProvider } from 'antd';\nimport zhCN from 'antd/locale/zh_CN';\nimport 'dayjs/locale/zh-cn';\nimport Layout from './components/Layout';\nimport Home from './pages/Home';\nimport Dashboard from './pages/Dashboard';\nimport TaskList from './pages/TaskList';\nimport TaskDetail from './pages/TaskDetail';\nimport AlertManagement from './pages/AlertManagement';\nimport ConfigManagement from './pages/ConfigManagement';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst App = () => {\n  return /*#__PURE__*/_jsxDEV(ConfigProvider, {\n    locale: zhCN,\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/home\",\n          element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 40\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/dashboard\",\n          element: /*#__PURE__*/_jsxDEV(Layout, {\n            children: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 53\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 45\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/tasks\",\n          element: /*#__PURE__*/_jsxDEV(Layout, {\n            children: /*#__PURE__*/_jsxDEV(TaskList, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 49\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/tasks/:id\",\n          element: /*#__PURE__*/_jsxDEV(Layout, {\n            children: /*#__PURE__*/_jsxDEV(TaskDetail, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 53\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 45\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/alerts\",\n          element: /*#__PURE__*/_jsxDEV(Layout, {\n            children: /*#__PURE__*/_jsxDEV(AlertManagement, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 50\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 42\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/config\",\n          element: /*#__PURE__*/_jsxDEV(Layout, {\n            children: /*#__PURE__*/_jsxDEV(ConfigManagement, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 50\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 42\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n};\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zhCN", "Layout", "Home", "Dashboard", "TaskList", "TaskDetail", "AlertManagement", "ConfigManagement", "jsxDEV", "_jsxDEV", "App", "locale", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Documents/浩鲸/ai培训/AI赋能资料(2)/任务3-作业计划巡检/inspection-system/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\r\nimport { ConfigProvider } from 'antd';\r\nimport zhCN from 'antd/locale/zh_CN';\r\nimport 'dayjs/locale/zh-cn';\r\n\r\nimport Layout from './components/Layout';\r\nimport Home from './pages/Home';\r\nimport Dashboard from './pages/Dashboard';\r\nimport TaskList from './pages/TaskList';\r\nimport TaskDetail from './pages/TaskDetail';\r\nimport AlertManagement from './pages/AlertManagement';\r\nimport ConfigManagement from './pages/ConfigManagement';\r\n\r\nimport './App.css';\r\n\r\nconst App: React.FC = () => {\r\n  return (\r\n    <ConfigProvider locale={zhCN}>\r\n      <Router>\r\n        <Routes>\r\n          {/* 首页独立，不使用Layout */}\r\n          <Route path=\"/\" element={<Home />} />\r\n          <Route path=\"/home\" element={<Home />} />\r\n          \r\n          {/* 其他页面使用Layout */}\r\n          <Route path=\"/dashboard\" element={<Layout><Dashboard /></Layout>} />\r\n          <Route path=\"/tasks\" element={<Layout><TaskList /></Layout>} />\r\n          <Route path=\"/tasks/:id\" element={<Layout><TaskDetail /></Layout>} />\r\n          <Route path=\"/alerts\" element={<Layout><AlertManagement /></Layout>} />\r\n          <Route path=\"/config\" element={<Layout><ConfigManagement /></Layout>} />\r\n        </Routes>\r\n      </Router>\r\n    </ConfigProvider>\r\n  );\r\n};\r\n\r\nexport default App;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAkB,kBAAkB;AACnF,SAASC,cAAc,QAAQ,MAAM;AACrC,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAO,oBAAoB;AAE3B,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,gBAAgB,MAAM,0BAA0B;AAEvD,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,MAAMC,GAAa,GAAGA,CAAA,KAAM;EAC1B,oBACED,OAAA,CAACV,cAAc;IAACY,MAAM,EAAEX,IAAK;IAAAY,QAAA,eAC3BH,OAAA,CAACb,MAAM;MAAAgB,QAAA,eACLH,OAAA,CAACZ,MAAM;QAAAe,QAAA,gBAELH,OAAA,CAACX,KAAK;UAACe,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEL,OAAA,CAACP,IAAI;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrCT,OAAA,CAACX,KAAK;UAACe,IAAI,EAAC,OAAO;UAACC,OAAO,eAAEL,OAAA,CAACP,IAAI;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGzCT,OAAA,CAACX,KAAK;UAACe,IAAI,EAAC,YAAY;UAACC,OAAO,eAAEL,OAAA,CAACR,MAAM;YAAAW,QAAA,eAACH,OAAA,CAACN,SAAS;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpET,OAAA,CAACX,KAAK;UAACe,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAEL,OAAA,CAACR,MAAM;YAAAW,QAAA,eAACH,OAAA,CAACL,QAAQ;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/DT,OAAA,CAACX,KAAK;UAACe,IAAI,EAAC,YAAY;UAACC,OAAO,eAAEL,OAAA,CAACR,MAAM;YAAAW,QAAA,eAACH,OAAA,CAACJ,UAAU;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrET,OAAA,CAACX,KAAK;UAACe,IAAI,EAAC,SAAS;UAACC,OAAO,eAAEL,OAAA,CAACR,MAAM;YAAAW,QAAA,eAACH,OAAA,CAACH,eAAe;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvET,OAAA,CAACX,KAAK;UAACe,IAAI,EAAC,SAAS;UAACC,OAAO,eAAEL,OAAA,CAACR,MAAM;YAAAW,QAAA,eAACH,OAAA,CAACF,gBAAgB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAErB,CAAC;AAACC,EAAA,GAnBIT,GAAa;AAqBnB,eAAeA,GAAG;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}