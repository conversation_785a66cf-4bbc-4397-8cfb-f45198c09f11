package com.inspection.system.service.impl;

import com.inspection.system.dto.DashboardStatsDTO;
import com.inspection.system.dto.InspectionTaskDTO;
import com.inspection.system.dto.AlertWorkOrderDTO;
import com.inspection.system.entity.InspectionTask;
import com.inspection.system.entity.AlertWorkOrder;
import com.inspection.system.repository.*;
import com.inspection.system.service.DashboardService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 仪表盘服务实现
 */
@Service
@RequiredArgsConstructor
public class DashboardServiceImpl implements DashboardService {

    private final DeviceRepository deviceRepository;
    private final RoomRepository roomRepository;
    private final InspectionTaskRepository inspectionTaskRepository;
    private final AlertWorkOrderRepository alertWorkOrderRepository;
    private final InspectionDetailRepository inspectionDetailRepository;

    @Override
    public DashboardStatsDTO getDashboardStats() {
        DashboardStatsDTO stats = new DashboardStatsDTO();

        // 基础统计数据
        stats.setTotalDevices(deviceRepository.count());
        stats.setTotalRooms(roomRepository.count());
        stats.setTodayTasks(inspectionTaskRepository.countTodayTasks());
        stats.setTodayCompletedTasks(inspectionTaskRepository.countTodayCompletedTasks());
        stats.setPendingAlerts(alertWorkOrderRepository.countPendingAlerts());
        stats.setCriticalAlerts(alertWorkOrderRepository.countCriticalAlerts());
        stats.setAbnormalTasks(inspectionTaskRepository.countAbnormalTasks());

        // 获取趋势数据
        stats.setTaskTrendData(getTaskTrend(7));
        stats.setAlertTrendData(getAlertDistribution());

        // 获取最近数据
        stats.setRecentTasks(getRecentTasks(5));
        stats.setRecentAlerts(getRecentAlerts(5));

        // 设置设备状态分布（模拟数据）
        Map<String, Long> deviceStatusStats = new HashMap<>();
        deviceStatusStats.put("ONLINE", deviceRepository.count() * 80 / 100);
        deviceStatusStats.put("OFFLINE", deviceRepository.count() * 15 / 100);
        deviceStatusStats.put("MAINTENANCE", deviceRepository.count() * 5 / 100);
        stats.setDeviceStatusStats(deviceStatusStats);

        // 设置任务状态分布
        Map<String, Long> taskStatusStats = new HashMap<>();
        taskStatusStats.put("PENDING", inspectionTaskRepository.countByStatus("PENDING"));
        taskStatusStats.put("IN_PROGRESS", inspectionTaskRepository.countByStatus("IN_PROGRESS"));
        taskStatusStats.put("COMPLETED", inspectionTaskRepository.countByStatus("COMPLETED"));
        taskStatusStats.put("FAILED", inspectionTaskRepository.countByStatus("FAILED"));
        stats.setTaskStatusStats(taskStatusStats);

        // 设置告警级别分布
        Map<String, Long> alertLevelStats = new HashMap<>();
        alertLevelStats.put("CRITICAL", alertWorkOrderRepository.countByAlertLevel("CRITICAL"));
        alertLevelStats.put("WARNING", alertWorkOrderRepository.countByAlertLevel("WARNING"));
        alertLevelStats.put("INFO", alertWorkOrderRepository.countByAlertLevel("INFO"));
        stats.setAlertLevelStats(alertLevelStats);

        return stats;
    }

    @Override
    public List<InspectionTaskDTO> getRecentTasks(int limit) {
        var recentTasks = inspectionTaskRepository.findRecentCompletedTasks(PageRequest.of(0, limit));
        return recentTasks.getContent().stream()
                .map(this::convertToTaskDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<AlertWorkOrderDTO> getRecentAlerts(int limit) {
        var recentAlerts = alertWorkOrderRepository.findRecentOrders(PageRequest.of(0, limit));
        return recentAlerts.getContent().stream()
                .map(this::convertToAlertDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<Map<String, Object>> getTaskTrend(int days) {
        LocalDateTime startDate = LocalDateTime.now().minusDays(days);
        List<Object[]> trendData = inspectionTaskRepository.getTaskTrendData(startDate);
        
        return trendData.stream()
                .filter(row -> row[0] != null && row[1] != null)
                .map(row -> {
                    Map<String, Object> data = new HashMap<>();
                    data.put("date", row[0].toString());
                    data.put("count", row[1]);
                    return data;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<Map<String, Object>> getAlertDistribution() {
        List<Object[]> alertLevelData = alertWorkOrderRepository.countByAlertLevel();
        
        return alertLevelData.stream()
                .filter(row -> row[0] != null && row[1] != null)
                .map(row -> {
                    Map<String, Object> data = new HashMap<>();
                    data.put("level", row[0].toString());
                    data.put("count", row[1]);
                    return data;
                })
                .collect(Collectors.toList());
    }

    private InspectionTaskDTO convertToTaskDTO(InspectionTask task) {
        InspectionTaskDTO dto = new InspectionTaskDTO();
        dto.setId(task.getId());
        dto.setTaskCode(task.getTaskCode());
        dto.setConfigId(task.getConfigId());
        dto.setTargetType(task.getTargetType().toString());
        dto.setTargetId(task.getTargetId());
        dto.setTaskType(task.getTaskType().toString());
        dto.setStatus(task.getStatus().toString());
        dto.setPriority(task.getPriority().toString());
        dto.setScheduledTime(task.getScheduledTime());
        dto.setStartTime(task.getStartTime());
        dto.setEndTime(task.getEndTime());
        dto.setResult(task.getResult() != null ? task.getResult().toString() : null);
        dto.setInspector(task.getInspector());
        dto.setCreateTime(task.getCreateTime());
        dto.setUpdateTime(task.getUpdateTime());
        return dto;
    }

    private AlertWorkOrderDTO convertToAlertDTO(AlertWorkOrder alert) {
        AlertWorkOrderDTO dto = new AlertWorkOrderDTO();
        dto.setId(alert.getId());
        dto.setOrderCode(alert.getOrderCode());
        dto.setTaskId(alert.getTaskId());
        dto.setTargetType(alert.getTargetType().toString());
        dto.setTargetId(alert.getTargetId());
        dto.setAlertLevel(alert.getAlertLevel().toString());
        dto.setAlertType(alert.getAlertType());
        dto.setAlertMessage(alert.getAlertMessage());
        dto.setStatus(alert.getStatus().toString());
        dto.setAssignee(alert.getAssignee());
        dto.setCreateTime(alert.getCreateTime());
        dto.setUpdateTime(alert.getUpdateTime());
        dto.setResolveTime(alert.getResolveTime());
        return dto;
    }
}
