import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, Row, Col, Typography, Space, Button } from 'antd';
import {
  DashboardOutlined,
  UnorderedListOutlined,
  SearchOutlined,
  SettingOutlined,
  AlertOutlined
} from '@ant-design/icons';

const { Title, Paragraph } = Typography;

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  features: string[];
  path: string;
  color: string;
}

const FeatureCard: React.FC<FeatureCardProps> = ({ icon, title, description, features, path, color }) => {
  const navigate = useNavigate();

  return (
    <Card
      hoverable
      className="feature-card"
      style={{ height: '100%' }}
      onClick={() => navigate(path)}
    >
      <div className="card-header" style={{ background: color }}>
        <div className="card-icon">{icon}</div>
      </div>
      <div className="card-content">
        <Title level={4} className="card-title">{title}</Title>
        <Paragraph className="card-description">{description}</Paragraph>
        <ul className="card-features">
          {features.map((feature, index) => (
            <li key={index}>{feature}</li>
          ))}
        </ul>
        <Button type="primary" className="modern-button" block>
          查看详情
        </Button>
      </div>
    </Card>
  );
};

const Home: React.FC = () => {
  const navigate = useNavigate();
  
  const featureCards: FeatureCardProps[] = [
    {
      icon: <DashboardOutlined />,
      title: '仪表盘',
      description: '系统概览页面，展示关键统计信息和实时数据',
      features: ['统计卡片展示', '图表可视化', '最近任务列表', '告警信息展示'],
      path: '/dashboard',
      color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    },
    {
      icon: <UnorderedListOutlined />,
      title: '巡检任务',
      description: '巡检任务管理页面，支持任务创建、执行和监控',
      features: ['任务列表展示', '多条件筛选', '状态管理', '批量操作'],
      path: '/tasks',
      color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
    },
    {
      icon: <SearchOutlined />,
      title: '任务详情',
      description: '任务详细信息页面，包含巡检结果和轨迹记录',
      features: ['任务基本信息', '巡检结果详情', '轨迹时间线', '相关告警'],
      path: '/tasks/1',
      color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
    },
    {
      icon: <SettingOutlined />,
      title: '配置管理',
      description: '巡检配置管理页面，支持配置创建和调度设置',
      features: ['配置列表管理', '启用/禁用开关', '新建配置', '频率设置'],
      path: '/config',
      color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'
    },
    {
      icon: <AlertOutlined />,
      title: '告警工单',
      description: '告警工单管理页面，处理异常告警和工单流程',
      features: ['告警统计', '告警列表', '处理状态', '告警升级'],
      path: '/alerts',
      color: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)'
    }
  ];

  return (
    <div className="home-page">
      {/* 简洁顶部导航栏 */}
      <div className="clean-header">
        <div className="header-container">
          <div className="brand">
            <div className="brand-logo">🔧</div>
            <div className="brand-name">设备巡检管理系统</div>
          </div>
          <nav className="nav-menu">
            <a href="#" className="nav-item active" onClick={(e) => { e.preventDefault(); navigate('/'); }}>首页</a>
            <a href="#" className="nav-item">文档</a>
            <a href="#" className="nav-item">关于</a>
          </nav>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="main-content">
        {/* 欢迎区域 */}
        <div className="welcome-section">
          <div className="welcome-container">
            <div className="welcome-badge">智能运维平台</div>
            <Title level={1} className="welcome-title">
              新一代设备巡检<br/>
              <span className="title-highlight">管理系统</span>
            </Title>
            <Paragraph className="welcome-description">
              基于AI驱动的智能巡检平台，为移动运营商提供全方位设备监控、
              自动化巡检、实时告警和数据分析服务，助力数字化运维转型。
            </Paragraph>
            <div className="welcome-actions">
              <Button type="primary" size="large" className="primary-btn" onClick={() => navigate('/dashboard')}>
                开始使用
              </Button>
              <Button size="large" className="secondary-btn">
                了解更多
              </Button>
            </div>
          </div>
          <div className="welcome-visual">
            <div className="visual-card card-1">
              <div className="card-icon">📊</div>
              <div className="card-label">实时监控</div>
            </div>
            <div className="visual-card card-2">
              <div className="card-icon">🔧</div>
              <div className="card-label">自动巡检</div>
            </div>
            <div className="visual-card card-3">
              <div className="card-icon">⚡</div>
              <div className="card-label">智能告警</div>
            </div>
          </div>
        </div>

        {/* 功能卡片区域 */}
        <div className="features-section">
          <div className="section-header">
            <Title level={2} className="section-title">核心功能</Title>
            <Paragraph className="section-desc">
              全面覆盖设备巡检全流程，提供一站式解决方案
            </Paragraph>
          </div>
          <div className="features-grid">
            {featureCards.map((card, index) => (
              <div key={index} className="feature-card" onClick={() => navigate(card.path)}>
                <div className="card-header">
                  <div className="card-icon" style={{background: card.color}}>
                    {card.icon}
                  </div>
                  <Title level={4} className="card-title">{card.title}</Title>
                </div>
                <div className="card-content">
                  <Paragraph className="card-desc">{card.description}</Paragraph>
                  <ul className="card-features">
                    {card.features.map((feature, idx) => (
                      <li key={idx}>{feature}</li>
                    ))}
                  </ul>
                </div>
                <div className="card-footer">
                  <Button type="link" className="card-btn">
                    查看详情 →
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 优势特点 */}
        <div className="advantages-section">
          <div className="section-header">
            <Title level={2} className="section-title">产品优势</Title>
            <Paragraph className="section-desc">
              专业技术团队，成熟产品方案，为您提供优质服务体验
            </Paragraph>
          </div>
          <div className="advantages-grid">
            <div className="advantage-item">
              <div className="advantage-icon">🚀</div>
              <div className="advantage-content">
                <Title level={4}>高效智能</Title>
                <Paragraph>AI驱动的智能巡检算法，大幅提升巡检效率和准确性</Paragraph>
              </div>
            </div>
            <div className="advantage-item">
              <div className="advantage-icon">🛡️</div>
              <div className="advantage-content">
                <Title level={4}>安全可靠</Title>
                <Paragraph>企业级安全保障，99.9%系统可用性，数据安全有保证</Paragraph>
              </div>
            </div>
            <div className="advantage-item">
              <div className="advantage-icon">📈</div>
              <div className="advantage-content">
                <Title level={4}>数据驱动</Title>
                <Paragraph>全面的数据分析和可视化，助力运维决策优化</Paragraph>
              </div>
            </div>
            <div className="advantage-item">
              <div className="advantage-icon">🔧</div>
              <div className="advantage-content">
                <Title level={4}>易于集成</Title>
                <Paragraph>标准化API接口，快速集成现有系统，降低部署成本</Paragraph>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 简洁底部 */}
      <div className="clean-footer">
        <div className="footer-container">
          <div className="footer-brand">
            <div className="footer-logo">
              <div className="logo-icon">🔧</div>
              <span>设备巡检管理系统</span>
            </div>
            <Paragraph className="footer-desc">
              专业的设备巡检管理解决方案，助力企业数字化转型
            </Paragraph>
          </div>
          <div className="footer-links">
            <div className="link-group">
              <Title level={5}>产品</Title>
              <a href="#" onClick={(e) => { e.preventDefault(); navigate('/dashboard'); }}>仪表盘</a>
              <a href="#" onClick={(e) => { e.preventDefault(); navigate('/tasks'); }}>巡检任务</a>
              <a href="#" onClick={(e) => { e.preventDefault(); navigate('/alerts'); }}>告警工单</a>
            </div>
            <div className="link-group">
              <Title level={5}>支持</Title>
              <a href="#">技术文档</a>
              <a href="#">API接口</a>
              <a href="#">联系我们</a>
            </div>
          </div>
        </div>
        <div className="footer-bottom">
          <Paragraph>© 2024 设备巡检管理系统. 保留所有权利.</Paragraph>
        </div>
      </div>
    </div>
  );
};

export default Home;