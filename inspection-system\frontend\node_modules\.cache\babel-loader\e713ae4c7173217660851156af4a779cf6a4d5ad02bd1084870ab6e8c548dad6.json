{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents\\\\\\u6D69\\u9CB8\\\\ai\\u57F9\\u8BAD\\\\AI\\u8D4B\\u80FD\\u8D44\\u6599(2)\\\\\\u4EFB\\u52A13-\\u4F5C\\u4E1A\\u8BA1\\u5212\\u5DE1\\u68C0\\\\inspection-system\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Layout as AntLayout, Menu, Button, theme } from 'antd';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { HomeOutlined, DashboardOutlined, UnorderedListOutlined, BellOutlined, SettingOutlined, MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Header,\n  Sider,\n  Content\n} = AntLayout;\nconst Layout = ({\n  children\n}) => {\n  _s();\n  const [collapsed, setCollapsed] = useState(false);\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    token: {\n      colorBgContainer\n    }\n  } = theme.useToken();\n  const menuItems = [{\n    key: '/',\n    icon: /*#__PURE__*/_jsxDEV(HomeOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 13\n    }, this),\n    label: '首页'\n  }, {\n    key: '/dashboard',\n    icon: /*#__PURE__*/_jsxDEV(DashboardOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 13\n    }, this),\n    label: '仪表盘'\n  }, {\n    key: '/tasks',\n    icon: /*#__PURE__*/_jsxDEV(UnorderedListOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 13\n    }, this),\n    label: '巡检任务'\n  }, {\n    key: '/alerts',\n    icon: /*#__PURE__*/_jsxDEV(BellOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 13\n    }, this),\n    label: '告警工单'\n  }, {\n    key: '/config',\n    icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 13\n    }, this),\n    label: '配置管理'\n  }];\n  const handleMenuClick = key => {\n    navigate(key);\n  };\n  return /*#__PURE__*/_jsxDEV(AntLayout, {\n    className: \"app-layout\",\n    children: [/*#__PURE__*/_jsxDEV(Sider, {\n      trigger: null,\n      collapsible: true,\n      collapsed: collapsed,\n      className: \"app-sider\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"app-logo\",\n        style: {\n          padding: '20px 16px',\n          fontSize: collapsed ? '16px' : '18px',\n          textAlign: 'center',\n          borderBottom: '1px solid rgba(255, 255, 255, 0.1)',\n          background: 'linear-gradient(45deg, #667eea, #764ba2)',\n          WebkitBackgroundClip: 'text',\n          WebkitTextFillColor: 'transparent',\n          backgroundClip: 'text',\n          fontWeight: 'bold'\n        },\n        children: collapsed ? '巡检' : '设备巡检管理系统'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Menu, {\n        theme: \"light\",\n        mode: \"inline\",\n        selectedKeys: [location.pathname],\n        items: menuItems,\n        onClick: ({\n          key\n        }) => handleMenuClick(key),\n        style: {\n          borderRight: 0,\n          background: 'transparent',\n          fontSize: '14px',\n          fontWeight: '500'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AntLayout, {\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        className: \"app-header\",\n        style: {\n          padding: 0,\n          background: 'rgba(255, 255, 255, 0.95)',\n          backdropFilter: 'blur(10px)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          paddingRight: '24px',\n          boxShadow: '0 2px 20px rgba(0, 0, 0, 0.1)',\n          borderBottom: '1px solid rgba(255, 255, 255, 0.2)'\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: collapsed ? /*#__PURE__*/_jsxDEV(MenuUnfoldOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 31\n          }, this) : /*#__PURE__*/_jsxDEV(MenuFoldOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 56\n          }, this),\n          onClick: () => setCollapsed(!collapsed),\n          style: {\n            fontSize: '18px',\n            width: 64,\n            height: 64,\n            color: '#667eea',\n            transition: 'all 0.3s ease'\n          },\n          className: \"modern-button\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Content, {\n        className: \"app-content\",\n        style: {\n          marginLeft: collapsed ? 80 : 200\n        },\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 61,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"Ky2kVOYUr8Hc2Desh5KexQUvG8k=\", false, function () {\n  return [useNavigate, useLocation, theme.useToken];\n});\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "useState", "Layout", "AntLayout", "<PERSON><PERSON>", "<PERSON><PERSON>", "theme", "useNavigate", "useLocation", "HomeOutlined", "DashboardOutlined", "UnorderedListOutlined", "BellOutlined", "SettingOutlined", "MenuFoldOutlined", "MenuUnfoldOutlined", "jsxDEV", "_jsxDEV", "Header", "<PERSON><PERSON>", "Content", "children", "_s", "collapsed", "setCollapsed", "navigate", "location", "token", "colorBgContainer", "useToken", "menuItems", "key", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "handleMenuClick", "className", "trigger", "collapsible", "style", "padding", "fontSize", "textAlign", "borderBottom", "background", "WebkitBackgroundClip", "WebkitTextFillColor", "backgroundClip", "fontWeight", "mode", "<PERSON><PERSON><PERSON><PERSON>", "pathname", "items", "onClick", "borderRight", "<PERSON><PERSON>ilter", "display", "alignItems", "justifyContent", "paddingRight", "boxShadow", "type", "width", "height", "color", "transition", "marginLeft", "_c", "$RefreshReg$"], "sources": ["D:/Documents/浩鲸/ai培训/AI赋能资料(2)/任务3-作业计划巡检/inspection-system/frontend/src/components/Layout.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Layout as AntLayout, <PERSON>u, Button, theme } from 'antd';\r\nimport { useNavigate, useLocation } from 'react-router-dom';\r\nimport {\r\n  HomeOutlined,\r\n  DashboardOutlined,\r\n  UnorderedListOutlined,\r\n  BellOutlined,\r\n  SettingOutlined,\r\n  MenuFoldOutlined,\r\n  MenuUnfoldOutlined,\r\n} from '@ant-design/icons';\r\n\r\nconst { Header, Sider, Content } = AntLayout;\r\n\r\ninterface LayoutProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\nconst Layout: React.FC<LayoutProps> = ({ children }) => {\r\n  const [collapsed, setCollapsed] = useState(false);\r\n  const navigate = useNavigate();\r\n  const location = useLocation();\r\n  const {\r\n    token: { colorBgContainer },\r\n  } = theme.useToken();\r\n\r\n  const menuItems = [\r\n    {\r\n      key: '/',\r\n      icon: <HomeOutlined />,\r\n      label: '首页',\r\n    },\r\n    {\r\n      key: '/dashboard',\r\n      icon: <DashboardOutlined />,\r\n      label: '仪表盘',\r\n    },\r\n    {\r\n      key: '/tasks',\r\n      icon: <UnorderedListOutlined />,\r\n      label: '巡检任务',\r\n    },\r\n    {\r\n      key: '/alerts',\r\n      icon: <BellOutlined />,\r\n      label: '告警工单',\r\n    },\r\n    {\r\n      key: '/config',\r\n      icon: <SettingOutlined />,\r\n      label: '配置管理',\r\n    },\r\n  ];\r\n\r\n  const handleMenuClick = (key: string) => {\r\n    navigate(key);\r\n  };\r\n\r\n  return (\r\n    <AntLayout className=\"app-layout\">\r\n      <Sider trigger={null} collapsible collapsed={collapsed} className=\"app-sider\">\r\n        <div className=\"app-logo\" style={{ \r\n          padding: '20px 16px', \r\n          fontSize: collapsed ? '16px' : '18px',\r\n          textAlign: 'center',\r\n          borderBottom: '1px solid rgba(255, 255, 255, 0.1)',\r\n          background: 'linear-gradient(45deg, #667eea, #764ba2)',\r\n          WebkitBackgroundClip: 'text',\r\n          WebkitTextFillColor: 'transparent',\r\n          backgroundClip: 'text',\r\n          fontWeight: 'bold'\r\n        }}>\r\n          {collapsed ? '巡检' : '设备巡检管理系统'}\r\n        </div>\r\n        <Menu\r\n          theme=\"light\"\r\n          mode=\"inline\"\r\n          selectedKeys={[location.pathname]}\r\n          items={menuItems}\r\n          onClick={({ key }) => handleMenuClick(key)}\r\n          style={{ \r\n            borderRight: 0, \r\n            background: 'transparent',\r\n            fontSize: '14px',\r\n            fontWeight: '500'\r\n          }}\r\n        />\r\n      </Sider>\r\n      <AntLayout>\r\n        <Header className=\"app-header\" style={{ \r\n          padding: 0, \r\n          background: 'rgba(255, 255, 255, 0.95)',\r\n          backdropFilter: 'blur(10px)',\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          justifyContent: 'space-between',\r\n          paddingRight: '24px',\r\n          boxShadow: '0 2px 20px rgba(0, 0, 0, 0.1)',\r\n          borderBottom: '1px solid rgba(255, 255, 255, 0.2)'\r\n        }}>\r\n          <Button\r\n            type=\"text\"\r\n            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\r\n            onClick={() => setCollapsed(!collapsed)}\r\n            style={{\r\n              fontSize: '18px',\r\n              width: 64,\r\n              height: 64,\r\n              color: '#667eea',\r\n              transition: 'all 0.3s ease'\r\n            }}\r\n            className=\"modern-button\"\r\n          />\r\n\r\n        </Header>\r\n        <Content className=\"app-content\" style={{ marginLeft: collapsed ? 80 : 200 }}>\r\n          {children}\r\n        </Content>\r\n      </AntLayout>\r\n    </AntLayout>\r\n  );\r\n};\r\n\r\nexport default Layout;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,IAAIC,SAAS,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,QAAQ,MAAM;AAC/D,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SACEC,YAAY,EACZC,iBAAiB,EACjBC,qBAAqB,EACrBC,YAAY,EACZC,eAAe,EACfC,gBAAgB,EAChBC,kBAAkB,QACb,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAM;EAAEC,MAAM;EAAEC,KAAK;EAAEC;AAAQ,CAAC,GAAGjB,SAAS;AAM5C,MAAMD,MAA6B,GAAGA,CAAC;EAAEmB;AAAS,CAAC,KAAK;EAAAC,EAAA;EACtD,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAMwB,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAMmB,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAM;IACJmB,KAAK,EAAE;MAAEC;IAAiB;EAC5B,CAAC,GAAGtB,KAAK,CAACuB,QAAQ,CAAC,CAAC;EAEpB,MAAMC,SAAS,GAAG,CAChB;IACEC,GAAG,EAAE,GAAG;IACRC,IAAI,eAAEf,OAAA,CAACR,YAAY;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,YAAY;IACjBC,IAAI,eAAEf,OAAA,CAACP,iBAAiB;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,QAAQ;IACbC,IAAI,eAAEf,OAAA,CAACN,qBAAqB;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC/BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,SAAS;IACdC,IAAI,eAAEf,OAAA,CAACL,YAAY;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,SAAS;IACdC,IAAI,eAAEf,OAAA,CAACJ,eAAe;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,eAAe,GAAIP,GAAW,IAAK;IACvCN,QAAQ,CAACM,GAAG,CAAC;EACf,CAAC;EAED,oBACEd,OAAA,CAACd,SAAS;IAACoC,SAAS,EAAC,YAAY;IAAAlB,QAAA,gBAC/BJ,OAAA,CAACE,KAAK;MAACqB,OAAO,EAAE,IAAK;MAACC,WAAW;MAAClB,SAAS,EAAEA,SAAU;MAACgB,SAAS,EAAC,WAAW;MAAAlB,QAAA,gBAC3EJ,OAAA;QAAKsB,SAAS,EAAC,UAAU;QAACG,KAAK,EAAE;UAC/BC,OAAO,EAAE,WAAW;UACpBC,QAAQ,EAAErB,SAAS,GAAG,MAAM,GAAG,MAAM;UACrCsB,SAAS,EAAE,QAAQ;UACnBC,YAAY,EAAE,oCAAoC;UAClDC,UAAU,EAAE,0CAA0C;UACtDC,oBAAoB,EAAE,MAAM;UAC5BC,mBAAmB,EAAE,aAAa;UAClCC,cAAc,EAAE,MAAM;UACtBC,UAAU,EAAE;QACd,CAAE;QAAA9B,QAAA,EACCE,SAAS,GAAG,IAAI,GAAG;MAAU;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC,eACNnB,OAAA,CAACb,IAAI;QACHE,KAAK,EAAC,OAAO;QACb8C,IAAI,EAAC,QAAQ;QACbC,YAAY,EAAE,CAAC3B,QAAQ,CAAC4B,QAAQ,CAAE;QAClCC,KAAK,EAAEzB,SAAU;QACjB0B,OAAO,EAAEA,CAAC;UAAEzB;QAAI,CAAC,KAAKO,eAAe,CAACP,GAAG,CAAE;QAC3CW,KAAK,EAAE;UACLe,WAAW,EAAE,CAAC;UACdV,UAAU,EAAE,aAAa;UACzBH,QAAQ,EAAE,MAAM;UAChBO,UAAU,EAAE;QACd;MAAE;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eACRnB,OAAA,CAACd,SAAS;MAAAkB,QAAA,gBACRJ,OAAA,CAACC,MAAM;QAACqB,SAAS,EAAC,YAAY;QAACG,KAAK,EAAE;UACpCC,OAAO,EAAE,CAAC;UACVI,UAAU,EAAE,2BAA2B;UACvCW,cAAc,EAAE,YAAY;UAC5BC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,eAAe;UAC/BC,YAAY,EAAE,MAAM;UACpBC,SAAS,EAAE,+BAA+B;UAC1CjB,YAAY,EAAE;QAChB,CAAE;QAAAzB,QAAA,eACAJ,OAAA,CAACZ,MAAM;UACL2D,IAAI,EAAC,MAAM;UACXhC,IAAI,EAAET,SAAS,gBAAGN,OAAA,CAACF,kBAAkB;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGnB,OAAA,CAACH,gBAAgB;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAChEoB,OAAO,EAAEA,CAAA,KAAMhC,YAAY,CAAC,CAACD,SAAS,CAAE;UACxCmB,KAAK,EAAE;YACLE,QAAQ,EAAE,MAAM;YAChBqB,KAAK,EAAE,EAAE;YACTC,MAAM,EAAE,EAAE;YACVC,KAAK,EAAE,SAAS;YAChBC,UAAU,EAAE;UACd,CAAE;UACF7B,SAAS,EAAC;QAAe;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEI,CAAC,eACTnB,OAAA,CAACG,OAAO;QAACmB,SAAS,EAAC,aAAa;QAACG,KAAK,EAAE;UAAE2B,UAAU,EAAE9C,SAAS,GAAG,EAAE,GAAG;QAAI,CAAE;QAAAF,QAAA,EAC1EA;MAAQ;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEhB,CAAC;AAACd,EAAA,CAvGIpB,MAA6B;EAAA,QAEhBK,WAAW,EACXC,WAAW,EAGxBF,KAAK,CAACuB,QAAQ;AAAA;AAAAyC,EAAA,GANdpE,MAA6B;AAyGnC,eAAeA,MAAM;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}