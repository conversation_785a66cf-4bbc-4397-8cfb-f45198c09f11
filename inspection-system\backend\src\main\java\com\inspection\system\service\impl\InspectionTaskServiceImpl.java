package com.inspection.system.service.impl;

import com.inspection.system.dto.InspectionTaskDTO;
import com.inspection.system.dto.PageResponse;
import com.inspection.system.entity.InspectionTask;
import com.inspection.system.repository.InspectionTaskRepository;
import com.inspection.system.service.InspectionTaskService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class InspectionTaskServiceImpl implements InspectionTaskService {

    private final InspectionTaskRepository taskRepository;

    @Override
    public PageResponse<InspectionTaskDTO> getTasksPage(String taskCode, String status, String taskType, 
                                                       String priority, String targetType, String inspector,
                                                       LocalDateTime startTime, LocalDateTime endTime, 
                                                       Pageable pageable) {
        InspectionTask.TaskStatus statusEnum = status != null ? InspectionTask.TaskStatus.valueOf(status) : null;
        InspectionTask.TaskType typeEnum = taskType != null ? InspectionTask.TaskType.valueOf(taskType) : null;
        InspectionTask.TaskPriority priorityEnum = priority != null ? InspectionTask.TaskPriority.valueOf(priority) : null;
        InspectionTask.TargetType targetEnum = targetType != null ? InspectionTask.TargetType.valueOf(targetType) : null;

        Page<InspectionTask> page = taskRepository.findByConditions(taskCode, statusEnum, typeEnum, priorityEnum, targetEnum, inspector, startTime, endTime, pageable);
        List<InspectionTaskDTO> dtos = page.getContent().stream().map(this::convertToDTO).collect(Collectors.toList());
        return new PageResponse<>(dtos, pageable.getPageNumber() + 1, pageable.getPageSize(), page.getTotalElements());
    }

    @Override
    public InspectionTaskDTO getTaskById(Long id) {
        return taskRepository.findById(id).map(this::convertToDTO).orElse(null);
    }

    @Override
    public InspectionTaskDTO createTask(InspectionTaskDTO taskDTO) {
        InspectionTask task = convertToEntity(taskDTO);
        task.setCreateTime(LocalDateTime.now());
task.setUpdateTime(LocalDateTime.now());
        task = taskRepository.save(task);
        return convertToDTO(task);
    }

    @Override
    public InspectionTaskDTO updateTask(Long id, InspectionTaskDTO taskDTO) {
        return taskRepository.findById(id).map(task -> {
            updateEntityFromDTO(task, taskDTO);
            task.setUpdateTime(LocalDateTime.now());
            return convertToDTO(taskRepository.save(task));
        }).orElse(null);
    }

    @Override
    public void deleteTask(Long id) {
        taskRepository.deleteById(id);
    }

    @Override
    public InspectionTaskDTO startTask(Long id, String inspector) {
        return taskRepository.findById(id).map(task -> {
            task.setStatus(InspectionTask.TaskStatus.IN_PROGRESS);
            task.setStartTime(LocalDateTime.now());
            task.setInspector(inspector);
            task.setUpdateTime(LocalDateTime.now());
            return convertToDTO(taskRepository.save(task));
        }).orElse(null);
    }

    @Override
    public InspectionTaskDTO completeTask(Long id, String result, String inspector) {
        return taskRepository.findById(id).map(task -> {
            task.setStatus(InspectionTask.TaskStatus.COMPLETED);
            task.setEndTime(LocalDateTime.now());
            task.setResult(InspectionTask.TaskResult.valueOf(result));
            task.setInspector(inspector);
            task.setUpdateTime(LocalDateTime.now());
            return convertToDTO(taskRepository.save(task));
        }).orElse(null);
    }

    @Override
    public InspectionTaskDTO pauseTask(Long id, String inspector) {
        return taskRepository.findById(id).map(task -> {
            task.setStatus(InspectionTask.TaskStatus.PENDING);
task.setUpdateTime(LocalDateTime.now());
            return convertToDTO(taskRepository.save(task));
        }).orElse(null);
    }

    @Override
    public InspectionTaskDTO retryTask(Long id, String inspector) {
        return taskRepository.findById(id).map(task -> {
            task.setStatus(InspectionTask.TaskStatus.PENDING);
            
            task.setUpdateTime(LocalDateTime.now());
            return convertToDTO(taskRepository.save(task));
        }).orElse(null);
    }

    @Override
    public void batchUpdateTaskStatus(List<Long> taskIds, InspectionTask.TaskStatus status, String operator) {
        List<InspectionTask> tasks = taskRepository.findAllById(taskIds);
        tasks.forEach(task -> {
            task.setStatus(status);
            task.setUpdateTime(LocalDateTime.now());
        });
        taskRepository.saveAll(tasks);
    }

    @Override
    public List<InspectionTaskDTO> getPendingTasks() {
        return taskRepository.findByStatus(InspectionTask.TaskStatus.PENDING).stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    @Override
    public List<InspectionTaskDTO> getInProgressTasks() {
        return taskRepository.findByStatus(InspectionTask.TaskStatus.IN_PROGRESS).stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    @Override
    public List<Object[]> getTaskStats() {
        return taskRepository.countByStatus();
    }

    private InspectionTaskDTO convertToDTO(InspectionTask task) {
        InspectionTaskDTO dto = new InspectionTaskDTO();
        dto.setId(task.getId());
        dto.setTaskCode(task.getTaskCode());
        dto.setConfigId(task.getConfigId());
        dto.setTaskType(task.getTaskType().name());
        dto.setTargetType(task.getTargetType().name());
        dto.setTargetId(task.getTargetId());
        dto.setScheduledTime(task.getScheduledTime());
        dto.setStartTime(task.getStartTime());
        dto.setEndTime(task.getEndTime());
        dto.setStatus(task.getStatus().name());
        dto.setPriority(task.getPriority().name());
        dto.setInspector(task.getInspector());
        dto.setResult(task.getResult() != null ? task.getResult().name() : null);
        
        dto.setCreateTime(task.getCreateTime());
dto.setUpdateTime(task.getUpdateTime());
        return dto;
    }

    private InspectionTask convertToEntity(InspectionTaskDTO dto) {
        InspectionTask task = new InspectionTask();
        updateEntityFromDTO(task, dto);
        return task;
    }

    private void updateEntityFromDTO(InspectionTask task, InspectionTaskDTO dto) {
        task.setTaskCode(dto.getTaskCode());
        task.setConfigId(dto.getConfigId());
        task.setTaskType(InspectionTask.TaskType.valueOf(dto.getTaskType()));
        task.setTargetType(InspectionTask.TargetType.valueOf(dto.getTargetType()));
        task.setTargetId(dto.getTargetId());
        task.setScheduledTime(dto.getScheduledTime());
        task.setPriority(InspectionTask.TaskPriority.valueOf(dto.getPriority()));
        task.setInspector(dto.getInspector());
        
        if (dto.getStatus() != null) {
            task.setStatus(InspectionTask.TaskStatus.valueOf(dto.getStatus()));
        }
        if (dto.getResult() != null) {
            task.setResult(InspectionTask.TaskResult.valueOf(dto.getResult()));
        }
    }
}