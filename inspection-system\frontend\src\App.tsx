import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import 'dayjs/locale/zh-cn';

import Layout from './components/Layout';
import Home from './pages/Home';
import Dashboard from './pages/Dashboard';
import TaskList from './pages/TaskList';
import TaskDetail from './pages/TaskDetail';
import AlertManagement from './pages/AlertManagement';
import ConfigManagement from './pages/ConfigManagement';

import './App.css';

const App: React.FC = () => {
  return (
    <ConfigProvider locale={zhCN}>
      <Router>
        <Routes>
          {/* 首页独立，不使用Layout */}
          <Route path="/" element={<Home />} />
          <Route path="/home" element={<Home />} />
          
          {/* 其他页面使用Layout */}
          <Route path="/dashboard" element={<Layout><Dashboard /></Layout>} />
          <Route path="/tasks" element={<Layout><TaskList /></Layout>} />
          <Route path="/tasks/:id" element={<Layout><TaskDetail /></Layout>} />
          <Route path="/alerts" element={<Layout><AlertManagement /></Layout>} />
          <Route path="/config" element={<Layout><ConfigManagement /></Layout>} />
        </Routes>
      </Router>
    </ConfigProvider>
  );
};

export default App;