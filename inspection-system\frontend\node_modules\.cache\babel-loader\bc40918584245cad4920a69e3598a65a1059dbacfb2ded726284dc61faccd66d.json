{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents\\\\\\u6D69\\u9CB8\\\\ai\\u57F9\\u8BAD\\\\AI\\u8D4B\\u80FD\\u8D44\\u6599(2)\\\\\\u4EFB\\u52A13-\\u4F5C\\u4E1A\\u8BA1\\u5212\\u5DE1\\u68C0\\\\inspection-system\\\\frontend\\\\src\\\\pages\\\\Dashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Row, Col, Card, Statistic, Table, Tag, Spin } from 'antd';\nimport { DatabaseOutlined, HomeOutlined, ClockCircleOutlined, AlertOutlined } from '@ant-design/icons';\nimport ReactECharts from 'echarts-for-react';\nimport { dashboardApi } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [stats, setStats] = useState(null);\n  useEffect(() => {\n    fetchDashboardStats();\n  }, []);\n  const fetchDashboardStats = async () => {\n    try {\n      setLoading(true);\n      const response = await dashboardApi.getStats();\n      if (response.data.success) {\n        setStats(response.data.data);\n      } else {\n        console.error('获取仪表盘数据失败:', response.data.message);\n        setStats(null);\n      }\n    } catch (error) {\n      console.error('获取仪表盘数据失败:', error);\n      setStats(null);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 任务趋势图表配置\n  const getTaskTrendOption = () => {\n    // 提供默认数据，防止图表塌陷\n    const defaultData = [{\n      date: '暂无数据',\n      count: 0\n    }];\n    const trendData = stats !== null && stats !== void 0 && stats.taskTrendData && stats.taskTrendData.length > 0 ? stats.taskTrendData : defaultData;\n    return {\n      title: {\n        text: '任务趋势',\n        left: 'center',\n        textStyle: {\n          fontSize: 16,\n          fontWeight: 'bold'\n        }\n      },\n      tooltip: {\n        trigger: 'axis',\n        formatter: function (params) {\n          var _params$, _params$2, _params$3, _params$4;\n          if (((_params$ = params[0]) === null || _params$ === void 0 ? void 0 : _params$.name) === '暂无数据') {\n            return '暂无任务趋势数据';\n          }\n          return `${(_params$2 = params[0]) === null || _params$2 === void 0 ? void 0 : _params$2.name}<br/>${(_params$3 = params[0]) === null || _params$3 === void 0 ? void 0 : _params$3.seriesName}: ${(_params$4 = params[0]) === null || _params$4 === void 0 ? void 0 : _params$4.value}`;\n        }\n      },\n      xAxis: {\n        type: 'category',\n        data: trendData.map(item => item.date)\n      },\n      yAxis: {\n        type: 'value',\n        min: 0\n      },\n      series: [{\n        name: '任务数量',\n        type: 'line',\n        data: trendData.map(item => item.count),\n        smooth: true,\n        itemStyle: {\n          color: '#1890ff'\n        },\n        lineStyle: {\n          color: '#1890ff'\n        }\n      }],\n      // 确保图表有最小高度\n      grid: {\n        top: 60,\n        bottom: 60,\n        left: 60,\n        right: 60\n      }\n    };\n  };\n\n  // 设备状态分布图表配置\n  const getDeviceStatusOption = () => {\n    // 提供默认数据，防止图表塌陷\n    const defaultData = [{\n      name: '暂无数据',\n      value: 1\n    }];\n    let data = defaultData;\n    if (stats !== null && stats !== void 0 && stats.deviceStatusStats && Object.keys(stats.deviceStatusStats).length > 0) {\n      data = Object.entries(stats.deviceStatusStats).map(([key, value]) => ({\n        name: key,\n        value: value\n      }));\n    }\n    return {\n      title: {\n        text: '设备状态分布',\n        left: 'center',\n        textStyle: {\n          fontSize: 16,\n          fontWeight: 'bold'\n        }\n      },\n      tooltip: {\n        trigger: 'item',\n        formatter: function (params) {\n          if (params.name === '暂无数据') {\n            return '暂无设备状态数据';\n          }\n          return `${params.seriesName}<br/>${params.name}: ${params.value} (${params.percent}%)`;\n        }\n      },\n      series: [{\n        name: '设备状态',\n        type: 'pie',\n        radius: ['30%', '70%'],\n        center: ['50%', '50%'],\n        data: data,\n        emphasis: {\n          itemStyle: {\n            shadowBlur: 10,\n            shadowOffsetX: 0,\n            shadowColor: 'rgba(0, 0, 0, 0.5)'\n          }\n        },\n        itemStyle: {\n          color: function (params) {\n            if (params.name === '暂无数据') {\n              return '#d9d9d9';\n            }\n            const colors = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1'];\n            return colors[params.dataIndex % colors.length];\n          }\n        }\n      }]\n    };\n  };\n\n  // 最近任务表格列配置\n  const taskColumns = [{\n    title: '任务编码',\n    dataIndex: 'taskCode',\n    key: 'taskCode'\n  }, {\n    title: '目标名称',\n    dataIndex: 'targetName',\n    key: 'targetName'\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    render: status => {\n      const colors = {\n        'PENDING': 'orange',\n        'IN_PROGRESS': 'blue',\n        'COMPLETED': 'green',\n        'FAILED': 'red'\n      };\n      const labels = {\n        'PENDING': '待执行',\n        'IN_PROGRESS': '执行中',\n        'COMPLETED': '已完成',\n        'FAILED': '执行失败'\n      };\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: colors[status],\n        children: labels[status]\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 16\n      }, this);\n    }\n  }, {\n    title: '优先级',\n    dataIndex: 'priority',\n    key: 'priority',\n    render: priority => {\n      const colors = {\n        'HIGH': 'red',\n        'NORMAL': 'green',\n        'LOW': 'default'\n      };\n      const labels = {\n        'HIGH': '高',\n        'NORMAL': '中',\n        'LOW': '低'\n      };\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: colors[priority],\n        children: labels[priority]\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 16\n      }, this);\n    }\n  }, {\n    title: '计划时间',\n    dataIndex: 'scheduledTime',\n    key: 'scheduledTime'\n  }];\n\n  // 最新告警表格列配置\n  const alertColumns = [{\n    title: '工单编码',\n    dataIndex: 'orderCode',\n    key: 'orderCode'\n  }, {\n    title: '告警级别',\n    dataIndex: 'alertLevel',\n    key: 'alertLevel',\n    render: level => {\n      const colors = {\n        'CRITICAL': 'red',\n        'WARNING': 'orange',\n        'INFO': 'blue'\n      };\n      const labels = {\n        'CRITICAL': '严重',\n        'WARNING': '警告',\n        'INFO': '信息'\n      };\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: colors[level],\n        children: labels[level]\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 16\n      }, this);\n    }\n  }, {\n    title: '告警类型',\n    dataIndex: 'alertType',\n    key: 'alertType'\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    render: status => {\n      const colors = {\n        'OPEN': 'orange',\n        'PROCESSING': 'blue',\n        'RESOLVED': 'green',\n        'CLOSED': 'default'\n      };\n      const labels = {\n        'OPEN': '待处理',\n        'PROCESSING': '处理中',\n        'RESOLVED': '已解决',\n        'CLOSED': '已关闭'\n      };\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: colors[status],\n        children: labels[status]\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 16\n      }, this);\n    }\n  }, {\n    title: '创建时间',\n    dataIndex: 'createTime',\n    key: 'createTime'\n  }];\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '60vh'\n      },\n      children: /*#__PURE__*/_jsxDEV(Spin, {\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 275,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: /*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"page-title gradient-text\",\n        children: \"\\u4EEA\\u8868\\u76D8\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      className: \"mb-24\",\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"stat-card modern-card hover-lift slide-in\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8BBE\\u5907\\u603B\\u6570\",\n            value: (stats === null || stats === void 0 ? void 0 : stats.totalDevices) || 0,\n            prefix: /*#__PURE__*/_jsxDEV(DatabaseOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#22c55e'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"stat-card modern-card hover-lift slide-in\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u673A\\u623F\\u603B\\u6570\",\n            value: (stats === null || stats === void 0 ? void 0 : stats.totalRooms) || 0,\n            prefix: /*#__PURE__*/_jsxDEV(HomeOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#3b82f6'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"stat-card modern-card hover-lift slide-in\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u4ECA\\u65E5\\u4EFB\\u52A1\",\n            value: (stats === null || stats === void 0 ? void 0 : stats.todayTasks) || 0,\n            prefix: /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#8b5cf6'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"stat-card modern-card hover-lift slide-in\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5F85\\u5904\\u7406\\u544A\\u8B66\",\n            value: (stats === null || stats === void 0 ? void 0 : stats.pendingAlerts) || 0,\n            prefix: /*#__PURE__*/_jsxDEV(AlertOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#ef4444'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 288,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      className: \"mb-24\",\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 16,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"modern-card hover-lift fade-in\",\n          title: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"gradient-text\",\n            children: \"\\u4EFB\\u52A1\\u8D8B\\u52BF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 20\n          }, this),\n          style: {\n            minHeight: '380px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              height: '300px',\n              minHeight: '300px',\n              position: 'relative'\n            },\n            children: /*#__PURE__*/_jsxDEV(ReactECharts, {\n              option: getTaskTrendOption(),\n              style: {\n                height: '100%',\n                width: '100%',\n                minHeight: '300px'\n              },\n              opts: {\n                renderer: 'canvas'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"modern-card hover-lift fade-in\",\n          title: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"gradient-text\",\n            children: \"\\u8BBE\\u5907\\u72B6\\u6001\\u5206\\u5E03\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 20\n          }, this),\n          style: {\n            minHeight: '380px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              height: '300px',\n              minHeight: '300px',\n              position: 'relative'\n            },\n            children: /*#__PURE__*/_jsxDEV(ReactECharts, {\n              option: getDeviceStatusOption(),\n              style: {\n                height: '100%',\n                width: '100%',\n                minHeight: '300px'\n              },\n              opts: {\n                renderer: 'canvas'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 332,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"gradient-text\",\n            children: \"\\u6700\\u8FD1\\u4EFB\\u52A1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 20\n          }, this),\n          extra: /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/tasks\",\n            className: \"gradient-text\",\n            children: \"\\u67E5\\u770B\\u66F4\\u591A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 20\n          }, this),\n          className: \"modern-card hover-lift fade-in\",\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            columns: taskColumns,\n            dataSource: (stats === null || stats === void 0 ? void 0 : stats.recentTasks) || [],\n            pagination: false,\n            size: \"small\",\n            rowKey: \"id\",\n            className: \"modern-scrollbar\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"gradient-text\",\n            children: \"\\u6700\\u65B0\\u544A\\u8B66\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 20\n          }, this),\n          extra: /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/alerts\",\n            className: \"gradient-text\",\n            children: \"\\u67E5\\u770B\\u66F4\\u591A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 20\n          }, this),\n          className: \"modern-card hover-lift fade-in\",\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            columns: alertColumns,\n            dataSource: (stats === null || stats === void 0 ? void 0 : stats.recentAlerts) || [],\n            pagination: false,\n            size: \"small\",\n            rowKey: \"id\",\n            className: \"modern-scrollbar\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 391,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 374,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 282,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"vZMOJxkaAUIiEQ08Aa7a/Gw2gZU=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Row", "Col", "Card", "Statistic", "Table", "Tag", "Spin", "DatabaseOutlined", "HomeOutlined", "ClockCircleOutlined", "Alert<PERSON>ut<PERSON>", "ReactECharts", "dashboardApi", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "loading", "setLoading", "stats", "setStats", "fetchDashboardStats", "response", "getStats", "data", "success", "console", "error", "message", "getTaskTrendOption", "defaultData", "date", "count", "trendData", "taskTrendData", "length", "title", "text", "left", "textStyle", "fontSize", "fontWeight", "tooltip", "trigger", "formatter", "params", "_params$", "_params$2", "_params$3", "_params$4", "name", "seriesName", "value", "xAxis", "type", "map", "item", "yAxis", "min", "series", "smooth", "itemStyle", "color", "lineStyle", "grid", "top", "bottom", "right", "getDeviceStatusOption", "deviceStatusStats", "Object", "keys", "entries", "key", "percent", "radius", "center", "emphasis", "<PERSON><PERSON><PERSON><PERSON>", "shadowOffsetX", "shadowColor", "colors", "dataIndex", "taskColumns", "render", "status", "labels", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "priority", "alertColumns", "level", "style", "display", "justifyContent", "alignItems", "height", "size", "className", "gutter", "xs", "sm", "md", "totalDevices", "prefix", "valueStyle", "totalRooms", "todayTasks", "<PERSON><PERSON><PERSON><PERSON>", "lg", "minHeight", "position", "option", "width", "opts", "renderer", "extra", "href", "columns", "dataSource", "recentTasks", "pagination", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "_c", "$RefreshReg$"], "sources": ["D:/Documents/浩鲸/ai培训/AI赋能资料(2)/任务3-作业计划巡检/inspection-system/frontend/src/pages/Dashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { Row, Col, Card, Statistic, Table, Tag, Spin } from 'antd';\r\nimport { \r\n  DatabaseOutlined, \r\n  HomeOutlined, \r\n  CheckCircleOutlined, \r\n  ExclamationCircleOutlined,\r\n  ClockCircleOutlined,\r\n  AlertOutlined\r\n} from '@ant-design/icons';\r\nimport ReactECharts from 'echarts-for-react';\r\nimport { dashboardApi } from '../services/api';\r\nimport { DashboardStats, RecentTask, RecentAlert } from '../types';\r\n\r\nconst Dashboard: React.FC = () => {\r\n  const [loading, setLoading] = useState(true);\r\n  const [stats, setStats] = useState<DashboardStats | null>(null);\r\n\r\n  useEffect(() => {\r\n    fetchDashboardStats();\r\n  }, []);\r\n\r\n  const fetchDashboardStats = async () => {\r\n    try {\r\n      setLoading(true);\r\n      const response = await dashboardApi.getStats();\r\n      if (response.data.success) {\r\n        setStats(response.data.data);\r\n      } else {\r\n        console.error('获取仪表盘数据失败:', response.data.message);\r\n        setStats(null);\r\n      }\r\n    } catch (error) {\r\n      console.error('获取仪表盘数据失败:', error);\r\n      setStats(null);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // 任务趋势图表配置\r\n  const getTaskTrendOption = () => {\r\n    // 提供默认数据，防止图表塌陷\r\n    const defaultData = [\r\n      { date: '暂无数据', count: 0 }\r\n    ];\r\n\r\n    const trendData = stats?.taskTrendData && stats.taskTrendData.length > 0\r\n      ? stats.taskTrendData\r\n      : defaultData;\r\n\r\n    return {\r\n      title: {\r\n        text: '任务趋势',\r\n        left: 'center',\r\n        textStyle: {\r\n          fontSize: 16,\r\n          fontWeight: 'bold'\r\n        }\r\n      },\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        formatter: function(params: any) {\r\n          if (params[0]?.name === '暂无数据') {\r\n            return '暂无任务趋势数据';\r\n          }\r\n          return `${params[0]?.name}<br/>${params[0]?.seriesName}: ${params[0]?.value}`;\r\n        }\r\n      },\r\n      xAxis: {\r\n        type: 'category',\r\n        data: trendData.map(item => item.date)\r\n      },\r\n      yAxis: {\r\n        type: 'value',\r\n        min: 0\r\n      },\r\n      series: [{\r\n        name: '任务数量',\r\n        type: 'line',\r\n        data: trendData.map(item => item.count),\r\n        smooth: true,\r\n        itemStyle: {\r\n          color: '#1890ff'\r\n        },\r\n        lineStyle: {\r\n          color: '#1890ff'\r\n        }\r\n      }],\r\n      // 确保图表有最小高度\r\n      grid: {\r\n        top: 60,\r\n        bottom: 60,\r\n        left: 60,\r\n        right: 60\r\n      }\r\n    };\r\n  };\r\n\r\n  // 设备状态分布图表配置\r\n  const getDeviceStatusOption = () => {\r\n    // 提供默认数据，防止图表塌陷\r\n    const defaultData = [\r\n      { name: '暂无数据', value: 1 }\r\n    ];\r\n\r\n    let data = defaultData;\r\n    if (stats?.deviceStatusStats && Object.keys(stats.deviceStatusStats).length > 0) {\r\n      data = Object.entries(stats.deviceStatusStats).map(([key, value]) => ({\r\n        name: key,\r\n        value: value\r\n      }));\r\n    }\r\n\r\n    return {\r\n      title: {\r\n        text: '设备状态分布',\r\n        left: 'center',\r\n        textStyle: {\r\n          fontSize: 16,\r\n          fontWeight: 'bold'\r\n        }\r\n      },\r\n      tooltip: {\r\n        trigger: 'item',\r\n        formatter: function(params: any) {\r\n          if (params.name === '暂无数据') {\r\n            return '暂无设备状态数据';\r\n          }\r\n          return `${params.seriesName}<br/>${params.name}: ${params.value} (${params.percent}%)`;\r\n        }\r\n      },\r\n      series: [{\r\n        name: '设备状态',\r\n        type: 'pie',\r\n        radius: ['30%', '70%'],\r\n        center: ['50%', '50%'],\r\n        data: data,\r\n        emphasis: {\r\n          itemStyle: {\r\n            shadowBlur: 10,\r\n            shadowOffsetX: 0,\r\n            shadowColor: 'rgba(0, 0, 0, 0.5)'\r\n          }\r\n        },\r\n        itemStyle: {\r\n          color: function(params: any) {\r\n            if (params.name === '暂无数据') {\r\n              return '#d9d9d9';\r\n            }\r\n            const colors = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1'];\r\n            return colors[params.dataIndex % colors.length];\r\n          }\r\n        }\r\n      }]\r\n    };\r\n  };\r\n\r\n  // 最近任务表格列配置\r\n  const taskColumns = [\r\n    {\r\n      title: '任务编码',\r\n      dataIndex: 'taskCode',\r\n      key: 'taskCode',\r\n    },\r\n    {\r\n      title: '目标名称',\r\n      dataIndex: 'targetName',\r\n      key: 'targetName',\r\n    },\r\n    {\r\n      title: '状态',\r\n      dataIndex: 'status',\r\n      key: 'status',\r\n      render: (status: string) => {\r\n        const colors = {\r\n          'PENDING': 'orange',\r\n          'IN_PROGRESS': 'blue',\r\n          'COMPLETED': 'green',\r\n          'FAILED': 'red'\r\n        };\r\n        const labels = {\r\n          'PENDING': '待执行',\r\n          'IN_PROGRESS': '执行中',\r\n          'COMPLETED': '已完成',\r\n          'FAILED': '执行失败'\r\n        };\r\n        return <Tag color={colors[status as keyof typeof colors]}>{labels[status as keyof typeof labels]}</Tag>;\r\n      },\r\n    },\r\n    {\r\n      title: '优先级',\r\n      dataIndex: 'priority',\r\n      key: 'priority',\r\n      render: (priority: string) => {\r\n        const colors = {\r\n          'HIGH': 'red',\r\n          'NORMAL': 'green',\r\n          'LOW': 'default'\r\n        };\r\n        const labels = {\r\n          'HIGH': '高',\r\n          'NORMAL': '中',\r\n          'LOW': '低'\r\n        };\r\n        return <Tag color={colors[priority as keyof typeof colors]}>{labels[priority as keyof typeof labels]}</Tag>;\r\n      },\r\n    },\r\n    {\r\n      title: '计划时间',\r\n      dataIndex: 'scheduledTime',\r\n      key: 'scheduledTime',\r\n    },\r\n  ];\r\n\r\n  // 最新告警表格列配置\r\n  const alertColumns = [\r\n    {\r\n      title: '工单编码',\r\n      dataIndex: 'orderCode',\r\n      key: 'orderCode',\r\n    },\r\n    {\r\n      title: '告警级别',\r\n      dataIndex: 'alertLevel',\r\n      key: 'alertLevel',\r\n      render: (level: string) => {\r\n        const colors = {\r\n          'CRITICAL': 'red',\r\n          'WARNING': 'orange',\r\n          'INFO': 'blue'\r\n        };\r\n        const labels = {\r\n          'CRITICAL': '严重',\r\n          'WARNING': '警告',\r\n          'INFO': '信息'\r\n        };\r\n        return <Tag color={colors[level as keyof typeof colors]}>{labels[level as keyof typeof labels]}</Tag>;\r\n      },\r\n    },\r\n    {\r\n      title: '告警类型',\r\n      dataIndex: 'alertType',\r\n      key: 'alertType',\r\n    },\r\n    {\r\n      title: '状态',\r\n      dataIndex: 'status',\r\n      key: 'status',\r\n      render: (status: string) => {\r\n        const colors = {\r\n          'OPEN': 'orange',\r\n          'PROCESSING': 'blue',\r\n          'RESOLVED': 'green',\r\n          'CLOSED': 'default'\r\n        };\r\n        const labels = {\r\n          'OPEN': '待处理',\r\n          'PROCESSING': '处理中',\r\n          'RESOLVED': '已解决',\r\n          'CLOSED': '已关闭'\r\n        };\r\n        return <Tag color={colors[status as keyof typeof colors]}>{labels[status as keyof typeof labels]}</Tag>;\r\n      },\r\n    },\r\n    {\r\n      title: '创建时间',\r\n      dataIndex: 'createTime',\r\n      key: 'createTime',\r\n    },\r\n  ];\r\n\r\n  if (loading) {\r\n    return (\r\n      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '60vh' }}>\r\n        <Spin size=\"large\" />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"fade-in\">\r\n      <div className=\"page-header\">\r\n        <h1 className=\"page-title gradient-text\">仪表盘</h1>\r\n      </div>\r\n\r\n      {/* 统计卡片 */}\r\n      <Row gutter={[16, 16]} className=\"mb-24\">\r\n        <Col xs={24} sm={12} md={6}>\r\n          <Card className=\"stat-card modern-card hover-lift slide-in\">\r\n            <Statistic\r\n              title=\"设备总数\"\r\n              value={stats?.totalDevices || 0}\r\n              prefix={<DatabaseOutlined />}\r\n              valueStyle={{ color: '#22c55e' }}\r\n            />\r\n          </Card>\r\n        </Col>\r\n        <Col xs={24} sm={12} md={6}>\r\n          <Card className=\"stat-card modern-card hover-lift slide-in\">\r\n            <Statistic\r\n              title=\"机房总数\"\r\n              value={stats?.totalRooms || 0}\r\n              prefix={<HomeOutlined />}\r\n              valueStyle={{ color: '#3b82f6' }}\r\n            />\r\n          </Card>\r\n        </Col>\r\n        <Col xs={24} sm={12} md={6}>\r\n          <Card className=\"stat-card modern-card hover-lift slide-in\">\r\n            <Statistic\r\n              title=\"今日任务\"\r\n              value={stats?.todayTasks || 0}\r\n              prefix={<ClockCircleOutlined />}\r\n              valueStyle={{ color: '#8b5cf6' }}\r\n            />\r\n          </Card>\r\n        </Col>\r\n        <Col xs={24} sm={12} md={6}>\r\n          <Card className=\"stat-card modern-card hover-lift slide-in\">\r\n            <Statistic\r\n              title=\"待处理告警\"\r\n              value={stats?.pendingAlerts || 0}\r\n              prefix={<AlertOutlined />}\r\n              valueStyle={{ color: '#ef4444' }}\r\n            />\r\n          </Card>\r\n        </Col>\r\n      </Row>\r\n\r\n      {/* 图表区域 */}\r\n      <Row gutter={[16, 16]} className=\"mb-24\">\r\n        <Col xs={24} lg={16}>\r\n          <Card\r\n            className=\"modern-card hover-lift fade-in\"\r\n            title={<span className=\"gradient-text\">任务趋势</span>}\r\n            style={{ minHeight: '380px' }}\r\n          >\r\n            <div style={{ height: '300px', minHeight: '300px', position: 'relative' }}>\r\n              <ReactECharts\r\n                option={getTaskTrendOption()}\r\n                style={{\r\n                  height: '100%',\r\n                  width: '100%',\r\n                  minHeight: '300px'\r\n                }}\r\n                opts={{ renderer: 'canvas' }}\r\n              />\r\n            </div>\r\n          </Card>\r\n        </Col>\r\n        <Col xs={24} lg={8}>\r\n          <Card\r\n            className=\"modern-card hover-lift fade-in\"\r\n            title={<span className=\"gradient-text\">设备状态分布</span>}\r\n            style={{ minHeight: '380px' }}\r\n          >\r\n            <div style={{ height: '300px', minHeight: '300px', position: 'relative' }}>\r\n              <ReactECharts\r\n                option={getDeviceStatusOption()}\r\n                style={{\r\n                  height: '100%',\r\n                  width: '100%',\r\n                  minHeight: '300px'\r\n                }}\r\n                opts={{ renderer: 'canvas' }}\r\n              />\r\n            </div>\r\n          </Card>\r\n        </Col>\r\n      </Row>\r\n\r\n      {/* 最近数据 */}\r\n      <Row gutter={[16, 16]}>\r\n        <Col xs={24} lg={12}>\r\n          <Card \r\n            title={<span className=\"gradient-text\">最近任务</span>} \r\n            extra={<a href=\"/tasks\" className=\"gradient-text\">查看更多</a>}\r\n            className=\"modern-card hover-lift fade-in\"\r\n          >\r\n            <Table\r\n              columns={taskColumns}\r\n              dataSource={stats?.recentTasks || []}\r\n              pagination={false}\r\n              size=\"small\"\r\n              rowKey=\"id\"\r\n              className=\"modern-scrollbar\"\r\n            />\r\n          </Card>\r\n        </Col>\r\n        <Col xs={24} lg={12}>\r\n          <Card \r\n            title={<span className=\"gradient-text\">最新告警</span>} \r\n            extra={<a href=\"/alerts\" className=\"gradient-text\">查看更多</a>}\r\n            className=\"modern-card hover-lift fade-in\"\r\n          >\r\n            <Table\r\n              columns={alertColumns}\r\n              dataSource={stats?.recentAlerts || []}\r\n              pagination={false}\r\n              size=\"small\"\r\n              rowKey=\"id\"\r\n              className=\"modern-scrollbar\"\r\n            />\r\n          </Card>\r\n        </Col>\r\n      </Row>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Dashboard;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,SAAS,EAAEC,KAAK,EAAEC,GAAG,EAAEC,IAAI,QAAQ,MAAM;AAClE,SACEC,gBAAgB,EAChBC,YAAY,EAGZC,mBAAmB,EACnBC,aAAa,QACR,mBAAmB;AAC1B,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,SAASC,YAAY,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG/C,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqB,KAAK,EAAEC,QAAQ,CAAC,GAAGtB,QAAQ,CAAwB,IAAI,CAAC;EAE/DC,SAAS,CAAC,MAAM;IACdsB,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMI,QAAQ,GAAG,MAAMV,YAAY,CAACW,QAAQ,CAAC,CAAC;MAC9C,IAAID,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBL,QAAQ,CAACE,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC;MAC9B,CAAC,MAAM;QACLE,OAAO,CAACC,KAAK,CAAC,YAAY,EAAEL,QAAQ,CAACE,IAAI,CAACI,OAAO,CAAC;QAClDR,QAAQ,CAAC,IAAI,CAAC;MAChB;IACF,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClCP,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMW,kBAAkB,GAAGA,CAAA,KAAM;IAC/B;IACA,MAAMC,WAAW,GAAG,CAClB;MAAEC,IAAI,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAE,CAAC,CAC3B;IAED,MAAMC,SAAS,GAAGd,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEe,aAAa,IAAIf,KAAK,CAACe,aAAa,CAACC,MAAM,GAAG,CAAC,GACpEhB,KAAK,CAACe,aAAa,GACnBJ,WAAW;IAEf,OAAO;MACLM,KAAK,EAAE;QACLC,IAAI,EAAE,MAAM;QACZC,IAAI,EAAE,QAAQ;QACdC,SAAS,EAAE;UACTC,QAAQ,EAAE,EAAE;UACZC,UAAU,EAAE;QACd;MACF,CAAC;MACDC,OAAO,EAAE;QACPC,OAAO,EAAE,MAAM;QACfC,SAAS,EAAE,SAAAA,CAASC,MAAW,EAAE;UAAA,IAAAC,QAAA,EAAAC,SAAA,EAAAC,SAAA,EAAAC,SAAA;UAC/B,IAAI,EAAAH,QAAA,GAAAD,MAAM,CAAC,CAAC,CAAC,cAAAC,QAAA,uBAATA,QAAA,CAAWI,IAAI,MAAK,MAAM,EAAE;YAC9B,OAAO,UAAU;UACnB;UACA,OAAO,IAAAH,SAAA,GAAGF,MAAM,CAAC,CAAC,CAAC,cAAAE,SAAA,uBAATA,SAAA,CAAWG,IAAI,SAAAF,SAAA,GAAQH,MAAM,CAAC,CAAC,CAAC,cAAAG,SAAA,uBAATA,SAAA,CAAWG,UAAU,MAAAF,SAAA,GAAKJ,MAAM,CAAC,CAAC,CAAC,cAAAI,SAAA,uBAATA,SAAA,CAAWG,KAAK,EAAE;QAC/E;MACF,CAAC;MACDC,KAAK,EAAE;QACLC,IAAI,EAAE,UAAU;QAChB9B,IAAI,EAAES,SAAS,CAACsB,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACzB,IAAI;MACvC,CAAC;MACD0B,KAAK,EAAE;QACLH,IAAI,EAAE,OAAO;QACbI,GAAG,EAAE;MACP,CAAC;MACDC,MAAM,EAAE,CAAC;QACPT,IAAI,EAAE,MAAM;QACZI,IAAI,EAAE,MAAM;QACZ9B,IAAI,EAAES,SAAS,CAACsB,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACxB,KAAK,CAAC;QACvC4B,MAAM,EAAE,IAAI;QACZC,SAAS,EAAE;UACTC,KAAK,EAAE;QACT,CAAC;QACDC,SAAS,EAAE;UACTD,KAAK,EAAE;QACT;MACF,CAAC,CAAC;MACF;MACAE,IAAI,EAAE;QACJC,GAAG,EAAE,EAAE;QACPC,MAAM,EAAE,EAAE;QACV5B,IAAI,EAAE,EAAE;QACR6B,KAAK,EAAE;MACT;IACF,CAAC;EACH,CAAC;;EAED;EACA,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClC;IACA,MAAMtC,WAAW,GAAG,CAClB;MAAEoB,IAAI,EAAE,MAAM;MAAEE,KAAK,EAAE;IAAE,CAAC,CAC3B;IAED,IAAI5B,IAAI,GAAGM,WAAW;IACtB,IAAIX,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEkD,iBAAiB,IAAIC,MAAM,CAACC,IAAI,CAACpD,KAAK,CAACkD,iBAAiB,CAAC,CAAClC,MAAM,GAAG,CAAC,EAAE;MAC/EX,IAAI,GAAG8C,MAAM,CAACE,OAAO,CAACrD,KAAK,CAACkD,iBAAiB,CAAC,CAACd,GAAG,CAAC,CAAC,CAACkB,GAAG,EAAErB,KAAK,CAAC,MAAM;QACpEF,IAAI,EAAEuB,GAAG;QACTrB,KAAK,EAAEA;MACT,CAAC,CAAC,CAAC;IACL;IAEA,OAAO;MACLhB,KAAK,EAAE;QACLC,IAAI,EAAE,QAAQ;QACdC,IAAI,EAAE,QAAQ;QACdC,SAAS,EAAE;UACTC,QAAQ,EAAE,EAAE;UACZC,UAAU,EAAE;QACd;MACF,CAAC;MACDC,OAAO,EAAE;QACPC,OAAO,EAAE,MAAM;QACfC,SAAS,EAAE,SAAAA,CAASC,MAAW,EAAE;UAC/B,IAAIA,MAAM,CAACK,IAAI,KAAK,MAAM,EAAE;YAC1B,OAAO,UAAU;UACnB;UACA,OAAO,GAAGL,MAAM,CAACM,UAAU,QAAQN,MAAM,CAACK,IAAI,KAAKL,MAAM,CAACO,KAAK,KAAKP,MAAM,CAAC6B,OAAO,IAAI;QACxF;MACF,CAAC;MACDf,MAAM,EAAE,CAAC;QACPT,IAAI,EAAE,MAAM;QACZI,IAAI,EAAE,KAAK;QACXqB,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;QACtBC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;QACtBpD,IAAI,EAAEA,IAAI;QACVqD,QAAQ,EAAE;UACRhB,SAAS,EAAE;YACTiB,UAAU,EAAE,EAAE;YACdC,aAAa,EAAE,CAAC;YAChBC,WAAW,EAAE;UACf;QACF,CAAC;QACDnB,SAAS,EAAE;UACTC,KAAK,EAAE,SAAAA,CAASjB,MAAW,EAAE;YAC3B,IAAIA,MAAM,CAACK,IAAI,KAAK,MAAM,EAAE;cAC1B,OAAO,SAAS;YAClB;YACA,MAAM+B,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;YACtE,OAAOA,MAAM,CAACpC,MAAM,CAACqC,SAAS,GAAGD,MAAM,CAAC9C,MAAM,CAAC;UACjD;QACF;MACF,CAAC;IACH,CAAC;EACH,CAAC;;EAED;EACA,MAAMgD,WAAW,GAAG,CAClB;IACE/C,KAAK,EAAE,MAAM;IACb8C,SAAS,EAAE,UAAU;IACrBT,GAAG,EAAE;EACP,CAAC,EACD;IACErC,KAAK,EAAE,MAAM;IACb8C,SAAS,EAAE,YAAY;IACvBT,GAAG,EAAE;EACP,CAAC,EACD;IACErC,KAAK,EAAE,IAAI;IACX8C,SAAS,EAAE,QAAQ;IACnBT,GAAG,EAAE,QAAQ;IACbW,MAAM,EAAGC,MAAc,IAAK;MAC1B,MAAMJ,MAAM,GAAG;QACb,SAAS,EAAE,QAAQ;QACnB,aAAa,EAAE,MAAM;QACrB,WAAW,EAAE,OAAO;QACpB,QAAQ,EAAE;MACZ,CAAC;MACD,MAAMK,MAAM,GAAG;QACb,SAAS,EAAE,KAAK;QAChB,aAAa,EAAE,KAAK;QACpB,WAAW,EAAE,KAAK;QAClB,QAAQ,EAAE;MACZ,CAAC;MACD,oBAAOxE,OAAA,CAACT,GAAG;QAACyD,KAAK,EAAEmB,MAAM,CAACI,MAAM,CAAyB;QAAAE,QAAA,EAAED,MAAM,CAACD,MAAM;MAAwB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IACzG;EACF,CAAC,EACD;IACEvD,KAAK,EAAE,KAAK;IACZ8C,SAAS,EAAE,UAAU;IACrBT,GAAG,EAAE,UAAU;IACfW,MAAM,EAAGQ,QAAgB,IAAK;MAC5B,MAAMX,MAAM,GAAG;QACb,MAAM,EAAE,KAAK;QACb,QAAQ,EAAE,OAAO;QACjB,KAAK,EAAE;MACT,CAAC;MACD,MAAMK,MAAM,GAAG;QACb,MAAM,EAAE,GAAG;QACX,QAAQ,EAAE,GAAG;QACb,KAAK,EAAE;MACT,CAAC;MACD,oBAAOxE,OAAA,CAACT,GAAG;QAACyD,KAAK,EAAEmB,MAAM,CAACW,QAAQ,CAAyB;QAAAL,QAAA,EAAED,MAAM,CAACM,QAAQ;MAAwB;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAC7G;EACF,CAAC,EACD;IACEvD,KAAK,EAAE,MAAM;IACb8C,SAAS,EAAE,eAAe;IAC1BT,GAAG,EAAE;EACP,CAAC,CACF;;EAED;EACA,MAAMoB,YAAY,GAAG,CACnB;IACEzD,KAAK,EAAE,MAAM;IACb8C,SAAS,EAAE,WAAW;IACtBT,GAAG,EAAE;EACP,CAAC,EACD;IACErC,KAAK,EAAE,MAAM;IACb8C,SAAS,EAAE,YAAY;IACvBT,GAAG,EAAE,YAAY;IACjBW,MAAM,EAAGU,KAAa,IAAK;MACzB,MAAMb,MAAM,GAAG;QACb,UAAU,EAAE,KAAK;QACjB,SAAS,EAAE,QAAQ;QACnB,MAAM,EAAE;MACV,CAAC;MACD,MAAMK,MAAM,GAAG;QACb,UAAU,EAAE,IAAI;QAChB,SAAS,EAAE,IAAI;QACf,MAAM,EAAE;MACV,CAAC;MACD,oBAAOxE,OAAA,CAACT,GAAG;QAACyD,KAAK,EAAEmB,MAAM,CAACa,KAAK,CAAyB;QAAAP,QAAA,EAAED,MAAM,CAACQ,KAAK;MAAwB;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IACvG;EACF,CAAC,EACD;IACEvD,KAAK,EAAE,MAAM;IACb8C,SAAS,EAAE,WAAW;IACtBT,GAAG,EAAE;EACP,CAAC,EACD;IACErC,KAAK,EAAE,IAAI;IACX8C,SAAS,EAAE,QAAQ;IACnBT,GAAG,EAAE,QAAQ;IACbW,MAAM,EAAGC,MAAc,IAAK;MAC1B,MAAMJ,MAAM,GAAG;QACb,MAAM,EAAE,QAAQ;QAChB,YAAY,EAAE,MAAM;QACpB,UAAU,EAAE,OAAO;QACnB,QAAQ,EAAE;MACZ,CAAC;MACD,MAAMK,MAAM,GAAG;QACb,MAAM,EAAE,KAAK;QACb,YAAY,EAAE,KAAK;QACnB,UAAU,EAAE,KAAK;QACjB,QAAQ,EAAE;MACZ,CAAC;MACD,oBAAOxE,OAAA,CAACT,GAAG;QAACyD,KAAK,EAAEmB,MAAM,CAACI,MAAM,CAAyB;QAAAE,QAAA,EAAED,MAAM,CAACD,MAAM;MAAwB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IACzG;EACF,CAAC,EACD;IACEvD,KAAK,EAAE,MAAM;IACb8C,SAAS,EAAE,YAAY;IACvBT,GAAG,EAAE;EACP,CAAC,CACF;EAED,IAAIxD,OAAO,EAAE;IACX,oBACEH,OAAA;MAAKiF,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEC,UAAU,EAAE,QAAQ;QAAEC,MAAM,EAAE;MAAO,CAAE;MAAAZ,QAAA,eAC9FzE,OAAA,CAACR,IAAI;QAAC8F,IAAI,EAAC;MAAO;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC;EAEV;EAEA,oBACE7E,OAAA;IAAKuF,SAAS,EAAC,SAAS;IAAAd,QAAA,gBACtBzE,OAAA;MAAKuF,SAAS,EAAC,aAAa;MAAAd,QAAA,eAC1BzE,OAAA;QAAIuF,SAAS,EAAC,0BAA0B;QAAAd,QAAA,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9C,CAAC,eAGN7E,OAAA,CAACd,GAAG;MAACsG,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACD,SAAS,EAAC,OAAO;MAAAd,QAAA,gBACtCzE,OAAA,CAACb,GAAG;QAACsG,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAlB,QAAA,eACzBzE,OAAA,CAACZ,IAAI;UAACmG,SAAS,EAAC,2CAA2C;UAAAd,QAAA,eACzDzE,OAAA,CAACX,SAAS;YACRiC,KAAK,EAAC,0BAAM;YACZgB,KAAK,EAAE,CAAAjC,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEuF,YAAY,KAAI,CAAE;YAChCC,MAAM,eAAE7F,OAAA,CAACP,gBAAgB;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC7BiB,UAAU,EAAE;cAAE9C,KAAK,EAAE;YAAU;UAAE;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN7E,OAAA,CAACb,GAAG;QAACsG,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAlB,QAAA,eACzBzE,OAAA,CAACZ,IAAI;UAACmG,SAAS,EAAC,2CAA2C;UAAAd,QAAA,eACzDzE,OAAA,CAACX,SAAS;YACRiC,KAAK,EAAC,0BAAM;YACZgB,KAAK,EAAE,CAAAjC,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE0F,UAAU,KAAI,CAAE;YAC9BF,MAAM,eAAE7F,OAAA,CAACN,YAAY;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBiB,UAAU,EAAE;cAAE9C,KAAK,EAAE;YAAU;UAAE;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN7E,OAAA,CAACb,GAAG;QAACsG,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAlB,QAAA,eACzBzE,OAAA,CAACZ,IAAI;UAACmG,SAAS,EAAC,2CAA2C;UAAAd,QAAA,eACzDzE,OAAA,CAACX,SAAS;YACRiC,KAAK,EAAC,0BAAM;YACZgB,KAAK,EAAE,CAAAjC,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE2F,UAAU,KAAI,CAAE;YAC9BH,MAAM,eAAE7F,OAAA,CAACL,mBAAmB;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChCiB,UAAU,EAAE;cAAE9C,KAAK,EAAE;YAAU;UAAE;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN7E,OAAA,CAACb,GAAG;QAACsG,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAlB,QAAA,eACzBzE,OAAA,CAACZ,IAAI;UAACmG,SAAS,EAAC,2CAA2C;UAAAd,QAAA,eACzDzE,OAAA,CAACX,SAAS;YACRiC,KAAK,EAAC,gCAAO;YACbgB,KAAK,EAAE,CAAAjC,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE4F,aAAa,KAAI,CAAE;YACjCJ,MAAM,eAAE7F,OAAA,CAACJ,aAAa;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1BiB,UAAU,EAAE;cAAE9C,KAAK,EAAE;YAAU;UAAE;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7E,OAAA,CAACd,GAAG;MAACsG,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACD,SAAS,EAAC,OAAO;MAAAd,QAAA,gBACtCzE,OAAA,CAACb,GAAG;QAACsG,EAAE,EAAE,EAAG;QAACS,EAAE,EAAE,EAAG;QAAAzB,QAAA,eAClBzE,OAAA,CAACZ,IAAI;UACHmG,SAAS,EAAC,gCAAgC;UAC1CjE,KAAK,eAAEtB,OAAA;YAAMuF,SAAS,EAAC,eAAe;YAAAd,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAE;UACnDI,KAAK,EAAE;YAAEkB,SAAS,EAAE;UAAQ,CAAE;UAAA1B,QAAA,eAE9BzE,OAAA;YAAKiF,KAAK,EAAE;cAAEI,MAAM,EAAE,OAAO;cAAEc,SAAS,EAAE,OAAO;cAAEC,QAAQ,EAAE;YAAW,CAAE;YAAA3B,QAAA,eACxEzE,OAAA,CAACH,YAAY;cACXwG,MAAM,EAAEtF,kBAAkB,CAAC,CAAE;cAC7BkE,KAAK,EAAE;gBACLI,MAAM,EAAE,MAAM;gBACdiB,KAAK,EAAE,MAAM;gBACbH,SAAS,EAAE;cACb,CAAE;cACFI,IAAI,EAAE;gBAAEC,QAAQ,EAAE;cAAS;YAAE;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN7E,OAAA,CAACb,GAAG;QAACsG,EAAE,EAAE,EAAG;QAACS,EAAE,EAAE,CAAE;QAAAzB,QAAA,eACjBzE,OAAA,CAACZ,IAAI;UACHmG,SAAS,EAAC,gCAAgC;UAC1CjE,KAAK,eAAEtB,OAAA;YAAMuF,SAAS,EAAC,eAAe;YAAAd,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAE;UACrDI,KAAK,EAAE;YAAEkB,SAAS,EAAE;UAAQ,CAAE;UAAA1B,QAAA,eAE9BzE,OAAA;YAAKiF,KAAK,EAAE;cAAEI,MAAM,EAAE,OAAO;cAAEc,SAAS,EAAE,OAAO;cAAEC,QAAQ,EAAE;YAAW,CAAE;YAAA3B,QAAA,eACxEzE,OAAA,CAACH,YAAY;cACXwG,MAAM,EAAE/C,qBAAqB,CAAC,CAAE;cAChC2B,KAAK,EAAE;gBACLI,MAAM,EAAE,MAAM;gBACdiB,KAAK,EAAE,MAAM;gBACbH,SAAS,EAAE;cACb,CAAE;cACFI,IAAI,EAAE;gBAAEC,QAAQ,EAAE;cAAS;YAAE;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7E,OAAA,CAACd,GAAG;MAACsG,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAAf,QAAA,gBACpBzE,OAAA,CAACb,GAAG;QAACsG,EAAE,EAAE,EAAG;QAACS,EAAE,EAAE,EAAG;QAAAzB,QAAA,eAClBzE,OAAA,CAACZ,IAAI;UACHkC,KAAK,eAAEtB,OAAA;YAAMuF,SAAS,EAAC,eAAe;YAAAd,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAE;UACnD4B,KAAK,eAAEzG,OAAA;YAAG0G,IAAI,EAAC,QAAQ;YAACnB,SAAS,EAAC,eAAe;YAAAd,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAE;UAC3DU,SAAS,EAAC,gCAAgC;UAAAd,QAAA,eAE1CzE,OAAA,CAACV,KAAK;YACJqH,OAAO,EAAEtC,WAAY;YACrBuC,UAAU,EAAE,CAAAvG,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEwG,WAAW,KAAI,EAAG;YACrCC,UAAU,EAAE,KAAM;YAClBxB,IAAI,EAAC,OAAO;YACZyB,MAAM,EAAC,IAAI;YACXxB,SAAS,EAAC;UAAkB;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN7E,OAAA,CAACb,GAAG;QAACsG,EAAE,EAAE,EAAG;QAACS,EAAE,EAAE,EAAG;QAAAzB,QAAA,eAClBzE,OAAA,CAACZ,IAAI;UACHkC,KAAK,eAAEtB,OAAA;YAAMuF,SAAS,EAAC,eAAe;YAAAd,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAE;UACnD4B,KAAK,eAAEzG,OAAA;YAAG0G,IAAI,EAAC,SAAS;YAACnB,SAAS,EAAC,eAAe;YAAAd,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAE;UAC5DU,SAAS,EAAC,gCAAgC;UAAAd,QAAA,eAE1CzE,OAAA,CAACV,KAAK;YACJqH,OAAO,EAAE5B,YAAa;YACtB6B,UAAU,EAAE,CAAAvG,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE2G,YAAY,KAAI,EAAG;YACtCF,UAAU,EAAE,KAAM;YAClBxB,IAAI,EAAC,OAAO;YACZyB,MAAM,EAAC,IAAI;YACXxB,SAAS,EAAC;UAAkB;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3E,EAAA,CA3YID,SAAmB;AAAAgH,EAAA,GAAnBhH,SAAmB;AA6YzB,eAAeA,SAAS;AAAC,IAAAgH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}