import { dashboardApi, taskApi, configApi, alertApi } from '../../services/api';
import request from '../../utils/request';

// Mock the request utility
jest.mock('../../utils/request');
const mockedRequest = request as jest.Mocked<typeof request>;

describe('API Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Dashboard API', () => {
    test('getStats calls correct endpoint', async () => {
      const mockResponse = {
        data: {
          totalTasks: 100,
          completedTasks: 80,
          pendingTasks: 15,
          failedTasks: 5,
        },
      };
      mockedRequest.get.mockResolvedValue(mockResponse);

      const result = await dashboardApi.getStats();

      expect(mockedRequest.get).toHaveBeenCalledWith('/dashboard/stats');
      expect(result).toEqual(mockResponse);
    });
  });

  describe('Task API', () => {
    test('getTasks calls correct endpoint with parameters', async () => {
      const mockResponse = {
        data: {
          content: [],
          totalElements: 0,
          totalPages: 0,
        },
      };
      mockedRequest.get.mockResolvedValue(mockResponse);

      const params = {
        current: 1,
        size: 10,
        taskCode: 'test',
        status: 'PENDING',
      };

      const result = await taskApi.getTasks(params);

      expect(mockedRequest.get).toHaveBeenCalledWith('/tasks', { params });
      expect(result).toEqual(mockResponse);
    });

    test('getTaskById calls correct endpoint', async () => {
      const mockResponse = {
        data: {
          id: 1,
          taskCode: 'TASK001',
          taskName: '测试任务',
        },
      };
      mockedRequest.get.mockResolvedValue(mockResponse);

      const result = await taskApi.getTaskById(1);

      expect(mockedRequest.get).toHaveBeenCalledWith('/tasks/1');
      expect(result).toEqual(mockResponse);
    });

    test('createTask calls correct endpoint with data', async () => {
      const mockResponse = {
        data: {
          id: 1,
          taskCode: 'TASK001',
        },
      };
      mockedRequest.post.mockResolvedValue(mockResponse);

      const taskData = {
        taskName: '新建任务',
        configId: 1,
        scheduledTime: '2024-01-01T10:00:00',
      };

      const result = await taskApi.createTask(taskData);

      expect(mockedRequest.post).toHaveBeenCalledWith('/tasks', taskData);
      expect(result).toEqual(mockResponse);
    });

    test('updateTask calls correct endpoint with data', async () => {
      const mockResponse = {
        data: {
          id: 1,
          taskCode: 'TASK001',
        },
      };
      mockedRequest.put.mockResolvedValue(mockResponse);

      const taskData = {
        taskName: '更新任务',
        status: 'COMPLETED',
      };

      const result = await taskApi.updateTask(1, taskData);

      expect(mockedRequest.put).toHaveBeenCalledWith('/tasks/1', taskData);
      expect(result).toEqual(mockResponse);
    });

    test('deleteTask calls correct endpoint', async () => {
      const mockResponse = { data: null };
      mockedRequest.delete.mockResolvedValue(mockResponse);

      const result = await taskApi.deleteTask(1);

      expect(mockedRequest.delete).toHaveBeenCalledWith('/tasks/1');
      expect(result).toEqual(mockResponse);
    });
  });

  describe('Config API', () => {
    test('getConfigs calls correct endpoint with parameters', async () => {
      const mockResponse = {
        data: {
          content: [],
          totalElements: 0,
        },
      };
      mockedRequest.get.mockResolvedValue(mockResponse);

      const params = {
        current: 1,
        size: 10,
        configName: 'test',
      };

      const result = await configApi.getConfigs(params);

      expect(mockedRequest.get).toHaveBeenCalledWith('/configs', { params });
      expect(result).toEqual(mockResponse);
    });

    test('createConfig calls correct endpoint with data', async () => {
      const mockResponse = {
        data: {
          id: 1,
          configName: '新建配置',
        },
      };
      mockedRequest.post.mockResolvedValue(mockResponse);

      const configData = {
        configName: '新建配置',
        targetType: 'DEVICE',
        targetId: 1,
      };

      const result = await configApi.createConfig(configData);

      expect(mockedRequest.post).toHaveBeenCalledWith('/configs', configData);
      expect(result).toEqual(mockResponse);
    });

    test('updateConfig calls correct endpoint with data', async () => {
      const mockResponse = {
        data: {
          id: 1,
          configName: '更新配置',
        },
      };
      mockedRequest.put.mockResolvedValue(mockResponse);

      const configData = {
        configName: '更新配置',
        status: 'ACTIVE',
      };

      const result = await configApi.updateConfig(1, configData);

      expect(mockedRequest.put).toHaveBeenCalledWith('/configs/1', configData);
      expect(result).toEqual(mockResponse);
    });

    test('deleteConfig calls correct endpoint', async () => {
      const mockResponse = { data: null };
      mockedRequest.delete.mockResolvedValue(mockResponse);

      const result = await configApi.deleteConfig(1);

      expect(mockedRequest.delete).toHaveBeenCalledWith('/configs/1');
      expect(result).toEqual(mockResponse);
    });
  });

  describe('Alert API', () => {
    test('getOrders calls correct endpoint with parameters', async () => {
      const mockResponse = {
        data: {
          content: [],
          totalElements: 0,
        },
      };
      mockedRequest.get.mockResolvedValue(mockResponse);

      const params = {
        current: 1,
        size: 10,
        alertLevel: 'WARNING',
      };

      const result = await alertApi.getOrders(params);

      expect(mockedRequest.get).toHaveBeenCalledWith('/alerts', { params });
      expect(result).toEqual(mockResponse);
    });

    test('updateOrder calls correct endpoint with data', async () => {
      const mockResponse = {
        data: {
          id: 1,
          status: 'RESOLVED',
        },
      };
      mockedRequest.put.mockResolvedValue(mockResponse);

      const alertData = {
        status: 'RESOLVED',
        resolveNote: '问题已解决',
      };

      const result = await alertApi.updateOrder(1, alertData);

      expect(mockedRequest.put).toHaveBeenCalledWith('/alerts/1', alertData);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('Error Handling', () => {
    test('handles network errors', async () => {
      const networkError = new Error('Network Error');
      mockedRequest.get.mockRejectedValue(networkError);

      await expect(dashboardApi.getStats()).rejects.toThrow('Network Error');
    });

    test('handles API errors', async () => {
      const apiError = {
        response: {
          status: 500,
          data: {
            message: 'Internal Server Error',
          },
        },
      };
      mockedRequest.get.mockRejectedValue(apiError);

      await expect(taskApi.getTasks({})).rejects.toEqual(apiError);
    });
  });
});