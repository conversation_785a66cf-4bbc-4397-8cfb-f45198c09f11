package com.inspection.system.repository;

import com.inspection.system.entity.InspectionConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 巡检配置数据访问层
 */
@Repository
public interface InspectionConfigRepository extends JpaRepository<InspectionConfig, Long>, JpaSpecificationExecutor<InspectionConfig> {

    /**
     * 根据配置名称查询配置
     */
    List<InspectionConfig> findByConfigNameContaining(String configName);

    /**
     * 根据目标类型查询配置
     */
    List<InspectionConfig> findByTargetType(InspectionConfig.TargetType targetType);

    /**
     * 根据目标类型和目标ID查询配置
     */
    List<InspectionConfig> findByTargetTypeAndTargetId(InspectionConfig.TargetType targetType, Long targetId);

    /**
     * 根据频率类型查询配置
     */
    List<InspectionConfig> findByFrequencyType(InspectionConfig.FrequencyType frequencyType);

    /**
     * 根据状态查询配置
     */
    List<InspectionConfig> findByStatus(InspectionConfig.ConfigStatus status);

    /**
     * 查询启用的自动巡检配置
     */
    List<InspectionConfig> findByAutoInspectionTrueAndStatus(InspectionConfig.ConfigStatus status);

    /**
     * 统计各频率类型配置数量
     */
    @Query("SELECT c.frequencyType, COUNT(c) FROM InspectionConfig c GROUP BY c.frequencyType")
    List<Object[]> countByFrequencyType();

    /**
     * 统计各状态配置数量
     */
    @Query("SELECT c.status, COUNT(c) FROM InspectionConfig c GROUP BY c.status")
    List<Object[]> countByStatus();

    /**
     * 统计各目标类型配置数量
     */
    @Query("SELECT c.targetType, COUNT(c) FROM InspectionConfig c GROUP BY c.targetType")
    List<Object[]> getConfigStatsByTargetType();

    /**
     * 多条件查询配置
     */
    @Query("SELECT c FROM InspectionConfig c WHERE " +
           "(:configName IS NULL OR c.configName LIKE %:configName%) AND " +
           "(:targetType IS NULL OR c.targetType = :targetType) AND " +
           "(:frequencyType IS NULL OR c.frequencyType = :frequencyType) AND " +
           "(:status IS NULL OR c.status = :status) AND " +
           "(:autoInspection IS NULL OR c.autoInspection = :autoInspection)")
    List<InspectionConfig> findByConditions(@Param("configName") String configName,
                                           @Param("targetType") InspectionConfig.TargetType targetType,
                                           @Param("frequencyType") InspectionConfig.FrequencyType frequencyType,
                                           @Param("status") InspectionConfig.ConfigStatus status,
                                           @Param("autoInspection") Boolean autoInspection);
}