import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Statistic, Table, Tag, Spin } from 'antd';
import { 
  DatabaseOutlined, 
  HomeOutlined, 
  CheckCircleOutlined, 
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  AlertOutlined
} from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import { dashboardApi } from '../services/api';
import { DashboardStats, RecentTask, RecentAlert } from '../types';

const Dashboard: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<DashboardStats | null>(null);

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      setLoading(true);
      const response = await dashboardApi.getStats();
      console.log('Dashboard API Response:', response);

      if (response.data && response.data.success) {
        const data = response.data.data;
        console.log('Dashboard Data:', data);
        setStats(data);
      } else {
        console.error('获取仪表盘数据失败:', response.data?.message || '未知错误');
        // 设置默认数据结构，防止页面塌陷
        setStats({
          totalDevices: 0,
          totalRooms: 0,
          todayTasks: 0,
          todayCompletedTasks: 0,
          pendingAlerts: 0,
          criticalAlerts: 0,
          abnormalTasks: 0,
          deviceStatusStats: {},
          taskStatusStats: {},
          alertLevelStats: {},
          taskTrendData: [],
          alertTrendData: [],
          recentTasks: [],
          recentAlerts: []
        });
      }
    } catch (error) {
      console.error('获取仪表盘数据失败:', error);
      // 设置默认数据结构，防止页面塌陷
      setStats({
        totalDevices: 0,
        totalRooms: 0,
        todayTasks: 0,
        todayCompletedTasks: 0,
        pendingAlerts: 0,
        criticalAlerts: 0,
        abnormalTasks: 0,
        deviceStatusStats: {},
        taskStatusStats: {},
        alertLevelStats: {},
        taskTrendData: [],
        alertTrendData: [],
        recentTasks: [],
        recentAlerts: []
      });
    } finally {
      setLoading(false);
    }
  };

  // 任务趋势图表配置
  const getTaskTrendOption = () => {
    // 提供默认数据，防止图表塌陷
    const defaultData = [
      { date: '暂无数据', count: 0 }
    ];

    const trendData = stats?.taskTrendData && stats.taskTrendData.length > 0
      ? stats.taskTrendData
      : defaultData;

    return {
      title: {
        text: '任务趋势',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'axis',
        formatter: function(params: any) {
          if (params[0]?.name === '暂无数据') {
            return '暂无任务趋势数据';
          }
          return `${params[0]?.name}<br/>${params[0]?.seriesName}: ${params[0]?.value}`;
        }
      },
      xAxis: {
        type: 'category',
        data: trendData.map(item => item.date)
      },
      yAxis: {
        type: 'value',
        min: 0
      },
      series: [{
        name: '任务数量',
        type: 'line',
        data: trendData.map(item => item.count),
        smooth: true,
        itemStyle: {
          color: '#1890ff'
        },
        lineStyle: {
          color: '#1890ff'
        }
      }],
      // 确保图表有最小高度
      grid: {
        top: 60,
        bottom: 60,
        left: 60,
        right: 60
      }
    };
  };

  // 设备状态分布图表配置
  const getDeviceStatusOption = () => {
    // 提供默认数据，防止图表塌陷
    const defaultData = [
      { name: '暂无数据', value: 1 }
    ];

    let data = defaultData;
    if (stats?.deviceStatusStats && Object.keys(stats.deviceStatusStats).length > 0) {
      data = Object.entries(stats.deviceStatusStats).map(([key, value]) => ({
        name: key,
        value: value
      }));
    }

    return {
      title: {
        text: '设备状态分布',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: function(params: any) {
          if (params.name === '暂无数据') {
            return '暂无设备状态数据';
          }
          return `${params.seriesName}<br/>${params.name}: ${params.value} (${params.percent}%)`;
        }
      },
      series: [{
        name: '设备状态',
        type: 'pie',
        radius: ['30%', '70%'],
        center: ['50%', '50%'],
        data: data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        itemStyle: {
          color: function(params: any) {
            if (params.name === '暂无数据') {
              return '#d9d9d9';
            }
            const colors = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1'];
            return colors[params.dataIndex % colors.length];
          }
        }
      }]
    };
  };

  // 最近任务表格列配置
  const taskColumns = [
    {
      title: '任务编码',
      dataIndex: 'taskCode',
      key: 'taskCode',
    },
    {
      title: '目标名称',
      dataIndex: 'targetName',
      key: 'targetName',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const colors = {
          'PENDING': 'orange',
          'IN_PROGRESS': 'blue',
          'COMPLETED': 'green',
          'FAILED': 'red'
        };
        const labels = {
          'PENDING': '待执行',
          'IN_PROGRESS': '执行中',
          'COMPLETED': '已完成',
          'FAILED': '执行失败'
        };
        return <Tag color={colors[status as keyof typeof colors]}>{labels[status as keyof typeof labels]}</Tag>;
      },
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority: string) => {
        const colors = {
          'HIGH': 'red',
          'NORMAL': 'green',
          'LOW': 'default'
        };
        const labels = {
          'HIGH': '高',
          'NORMAL': '中',
          'LOW': '低'
        };
        return <Tag color={colors[priority as keyof typeof colors]}>{labels[priority as keyof typeof labels]}</Tag>;
      },
    },
    {
      title: '计划时间',
      dataIndex: 'scheduledTime',
      key: 'scheduledTime',
    },
  ];

  // 最新告警表格列配置
  const alertColumns = [
    {
      title: '工单编码',
      dataIndex: 'orderCode',
      key: 'orderCode',
    },
    {
      title: '告警级别',
      dataIndex: 'alertLevel',
      key: 'alertLevel',
      render: (level: string) => {
        const colors = {
          'CRITICAL': 'red',
          'WARNING': 'orange',
          'INFO': 'blue'
        };
        const labels = {
          'CRITICAL': '严重',
          'WARNING': '警告',
          'INFO': '信息'
        };
        return <Tag color={colors[level as keyof typeof colors]}>{labels[level as keyof typeof labels]}</Tag>;
      },
    },
    {
      title: '告警类型',
      dataIndex: 'alertType',
      key: 'alertType',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const colors = {
          'OPEN': 'orange',
          'PROCESSING': 'blue',
          'RESOLVED': 'green',
          'CLOSED': 'default'
        };
        const labels = {
          'OPEN': '待处理',
          'PROCESSING': '处理中',
          'RESOLVED': '已解决',
          'CLOSED': '已关闭'
        };
        return <Tag color={colors[status as keyof typeof colors]}>{labels[status as keyof typeof labels]}</Tag>;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
    },
  ];

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '60vh' }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="fade-in">
      <div className="page-header">
        <h1 className="page-title gradient-text">仪表盘</h1>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} className="mb-24">
        <Col xs={24} sm={12} md={6}>
          <Card className="stat-card modern-card hover-lift slide-in">
            <Statistic
              title="设备总数"
              value={stats?.totalDevices || 0}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: '#22c55e' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card className="stat-card modern-card hover-lift slide-in">
            <Statistic
              title="机房总数"
              value={stats?.totalRooms || 0}
              prefix={<HomeOutlined />}
              valueStyle={{ color: '#3b82f6' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card className="stat-card modern-card hover-lift slide-in">
            <Statistic
              title="今日任务"
              value={stats?.todayTasks || 0}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#8b5cf6' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card className="stat-card modern-card hover-lift slide-in">
            <Statistic
              title="待处理告警"
              value={stats?.pendingAlerts || 0}
              prefix={<AlertOutlined />}
              valueStyle={{ color: '#ef4444' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 图表区域 */}
      <Row gutter={[16, 16]} className="mb-24">
        <Col xs={24} lg={16}>
          <Card
            className="modern-card hover-lift fade-in"
            title={<span className="gradient-text">任务趋势</span>}
            style={{ minHeight: '380px' }}
          >
            <div style={{ height: '300px', minHeight: '300px', position: 'relative' }}>
              <ReactECharts
                option={getTaskTrendOption()}
                style={{
                  height: '100%',
                  width: '100%',
                  minHeight: '300px'
                }}
                opts={{ renderer: 'canvas' }}
              />
            </div>
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card
            className="modern-card hover-lift fade-in"
            title={<span className="gradient-text">设备状态分布</span>}
            style={{ minHeight: '380px' }}
          >
            <div style={{ height: '300px', minHeight: '300px', position: 'relative' }}>
              <ReactECharts
                option={getDeviceStatusOption()}
                style={{
                  height: '100%',
                  width: '100%',
                  minHeight: '300px'
                }}
                opts={{ renderer: 'canvas' }}
              />
            </div>
          </Card>
        </Col>
      </Row>

      {/* 最近数据 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card 
            title={<span className="gradient-text">最近任务</span>} 
            extra={<a href="/tasks" className="gradient-text">查看更多</a>}
            className="modern-card hover-lift fade-in"
          >
            <Table
              columns={taskColumns}
              dataSource={stats?.recentTasks || []}
              pagination={false}
              size="small"
              rowKey="id"
              className="modern-scrollbar"
            />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card 
            title={<span className="gradient-text">最新告警</span>} 
            extra={<a href="/alerts" className="gradient-text">查看更多</a>}
            className="modern-card hover-lift fade-in"
          >
            <Table
              columns={alertColumns}
              dataSource={stats?.recentAlerts || []}
              pagination={false}
              size="small"
              rowKey="id"
              className="modern-scrollbar"
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;