package com.inspection.system.integration;

import com.inspection.system.entity.*;
import com.inspection.system.repository.*;
import com.inspection.system.service.AutoInspectionService;
import com.inspection.system.service.SchedulerService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 巡检工作流集成测试
 */
@SpringBootTest
@ActiveProfiles("test")
class InspectionWorkflowIntegrationTest {

    @Autowired
    private SchedulerService schedulerService;
    
    @Autowired
    private AutoInspectionService autoInspectionService;

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private InspectionConfigRepository configRepository;
    
    @Autowired
    private InspectionTaskRepository taskRepository;
    
    @Autowired
    private InspectionDetailRepository detailRepository;
    
    @Autowired
    private InspectionTrackRepository trackRepository;
    
    @Autowired
    private AlertWorkOrderRepository alertRepository;
    
    @Autowired
    private DeviceRepository deviceRepository;
    
    @Autowired
    private RoomRepository roomRepository;
    
    @Autowired
    private ThresholdConfigRepository thresholdRepository;

    private Device testDevice;
    private Room testRoom;
    private InspectionConfig deviceConfig;
    private InspectionConfig roomConfig;

    @BeforeEach
    void setUp() {
        // 清理数据
        alertRepository.deleteAll();
        trackRepository.deleteAll();
        detailRepository.deleteAll();
        taskRepository.deleteAll();
        thresholdRepository.deleteAll();
        configRepository.deleteAll();
        deviceRepository.deleteAll();
        roomRepository.deleteAll();

        // 创建测试设备
        testDevice = new Device();
        testDevice.setDeviceCode("DEV001-" + System.currentTimeMillis());
        testDevice.setDeviceName("测试主机服务器");
        testDevice.setDeviceType(Device.DeviceType.HOST);
        testDevice.setIpAddress("*************");
        // 先创建测试机房
        testRoom = new Room();
        testRoom.setRoomCode("ROOM001-" + System.currentTimeMillis());
        testRoom.setRoomName("A栋1楼机房");
        testRoom.setLocation("A栋1楼");
        testRoom.setStatus(Room.RoomStatus.NORMAL);
        testRoom = roomRepository.save(testRoom);

        // 设置设备的机房关联
        testDevice.setRoom(testRoom);
        testDevice.setStatus(Device.DeviceStatus.NORMAL);
        testDevice = deviceRepository.save(testDevice);

        // 创建设备巡检配置
        deviceConfig = new InspectionConfig();
        deviceConfig.setConfigName("主机每日巡检");
        deviceConfig.setTargetType(InspectionConfig.TargetType.DEVICE);
        deviceConfig.setTargetId(testDevice.getId());
        deviceConfig.setFrequencyType(InspectionConfig.FrequencyType.DAILY);
        deviceConfig.setFrequencyValue(1);
        deviceConfig.setAutoInspection(true);
        deviceConfig.setStatus(InspectionConfig.ConfigStatus.ACTIVE);
        deviceConfig.setCreatedAt(LocalDateTime.now());
        deviceConfig = configRepository.save(deviceConfig);

        // 创建机房巡检配置
        roomConfig = new InspectionConfig();
        roomConfig.setConfigName("机房每日巡检");
        roomConfig.setTargetType(InspectionConfig.TargetType.ROOM);
        roomConfig.setTargetId(testRoom.getId());
        roomConfig.setFrequencyType(InspectionConfig.FrequencyType.DAILY);
        roomConfig.setFrequencyValue(1);
        roomConfig.setAutoInspection(true);
        roomConfig.setStatus(InspectionConfig.ConfigStatus.ACTIVE);
        roomConfig.setCreatedAt(LocalDateTime.now());
        roomConfig = configRepository.save(roomConfig);

        // 创建阈值配置
        createThresholdConfigs();
    }

    private void createThresholdConfigs() {
        // CPU使用率阈值
        ThresholdConfig cpuThreshold = new ThresholdConfig();
        cpuThreshold.setTargetType(ThresholdConfig.TargetType.DEVICE);
        cpuThreshold.setTargetId(testDevice.getId());
        cpuThreshold.setItemName("CPU_USAGE");
        cpuThreshold.setThresholdMax(new BigDecimal("80.0"));
        cpuThreshold.setAlertLevel(ThresholdConfig.AlertLevel.WARNING);
        thresholdRepository.save(cpuThreshold);

        // 内存使用率阈值
        ThresholdConfig memoryThreshold = new ThresholdConfig();
        memoryThreshold.setTargetType(ThresholdConfig.TargetType.DEVICE);
        memoryThreshold.setTargetId(testDevice.getId());
        memoryThreshold.setItemName("MEMORY_USAGE");
        memoryThreshold.setThresholdMax(new BigDecimal("85.0"));
        memoryThreshold.setAlertLevel(ThresholdConfig.AlertLevel.WARNING);
        thresholdRepository.save(memoryThreshold);

        // 机房温度阈值
        ThresholdConfig temperatureThreshold = new ThresholdConfig();
        temperatureThreshold.setTargetType(ThresholdConfig.TargetType.ROOM);
        temperatureThreshold.setTargetId(testRoom.getId());
        temperatureThreshold.setItemName("TEMPERATURE");
        temperatureThreshold.setThresholdMax(new BigDecimal("28.0"));
        temperatureThreshold.setAlertLevel(ThresholdConfig.AlertLevel.WARNING);
        thresholdRepository.save(temperatureThreshold);
    }

    @Test
    void testCompleteInspectionWorkflow() {
        // 1. 生成周期性任务
        schedulerService.generatePeriodicInspectionTasks();
        
        // 验证任务生成
        List<InspectionTask> tasks = taskRepository.findAll();
        assertEquals(2, tasks.size(), "应该生成2个巡检任务（设备+机房）");
        
        InspectionTask deviceTask = tasks.stream()
                .filter(t -> t.getTargetType() == InspectionTask.TargetType.DEVICE)
                .findFirst().orElse(null);
        InspectionTask roomTask = tasks.stream()
                .filter(t -> t.getTargetType() == InspectionTask.TargetType.ROOM)
                .findFirst().orElse(null);
        
        assertNotNull(deviceTask, "设备巡检任务应该存在");
        assertNotNull(roomTask, "机房巡检任务应该存在");
        assertEquals(InspectionTask.TaskStatus.PENDING, deviceTask.getStatus());
        assertEquals(InspectionTask.TaskStatus.PENDING, roomTask.getStatus());

        // 2. 执行自动巡检
        schedulerService.executeAutoInspection();
        
        // 验证任务执行
        deviceTask = taskRepository.findById(deviceTask.getId()).orElse(null);
        roomTask = taskRepository.findById(roomTask.getId()).orElse(null);
        
        assertNotNull(deviceTask);
        assertNotNull(roomTask);
        assertEquals(InspectionTask.TaskStatus.COMPLETED, deviceTask.getStatus());
        assertEquals(InspectionTask.TaskStatus.COMPLETED, roomTask.getStatus());
        assertEquals("SYSTEM", deviceTask.getInspector());
        assertEquals("SYSTEM", roomTask.getInspector());

        // 3. 验证巡检详情
        List<InspectionDetail> deviceDetails = detailRepository.findByTaskId(deviceTask.getId());
        List<InspectionDetail> roomDetails = detailRepository.findByTaskId(roomTask.getId());
        
        assertEquals(5, deviceDetails.size(), "主机巡检应该有5个检查项目");
        assertEquals(4, roomDetails.size(), "机房巡检应该有4个检查项目");
        
        // 验证检查项目
        assertTrue(deviceDetails.stream().anyMatch(d -> "CPU使用率".equals(d.getItemName())));
        assertTrue(deviceDetails.stream().anyMatch(d -> "内存使用率".equals(d.getItemName())));
        assertTrue(deviceDetails.stream().anyMatch(d -> "存储使用率".equals(d.getItemName())));
        assertTrue(deviceDetails.stream().anyMatch(d -> "安全漏洞检查".equals(d.getItemName())));
        assertTrue(deviceDetails.stream().anyMatch(d -> "网络连通性".equals(d.getItemName())));
        
        assertTrue(roomDetails.stream().anyMatch(d -> "机房温度".equals(d.getItemName())));
        assertTrue(roomDetails.stream().anyMatch(d -> "机房湿度".equals(d.getItemName())));
        assertTrue(roomDetails.stream().anyMatch(d -> "电力系统".equals(d.getItemName())));
        assertTrue(roomDetails.stream().anyMatch(d -> "安防系统".equals(d.getItemName())));

        // 4. 验证巡检轨迹
        List<InspectionTrack> deviceTracks = trackRepository.findByTaskIdOrderByCreateTimeAsc(deviceTask.getId());
        List<InspectionTrack> roomTracks = trackRepository.findByTaskIdOrderByCreateTimeAsc(roomTask.getId());
        
        assertTrue(deviceTracks.size() >= 7, "设备巡检应该有至少7条轨迹记录");
        assertTrue(roomTracks.size() >= 6, "机房巡检应该有至少6条轨迹记录");
        
        // 验证轨迹顺序
        assertEquals("START", deviceTracks.get(0).getAction());
        assertEquals("COMPLETE", deviceTracks.get(deviceTracks.size() - 1).getAction());

        // 5. 验证告警生成（可能有也可能没有，取决于随机值）
        List<AlertWorkOrder> alerts = alertRepository.findAll();
        // 由于使用随机值模拟，告警数量不确定，但如果有告警，应该有正确的属性
        for (AlertWorkOrder alert : alerts) {
            assertNotNull(alert.getOrderCode());
            assertNotNull(alert.getTargetType());
            assertNotNull(alert.getTargetId());
            assertNotNull(alert.getAlertMessage());
            assertNotNull(alert.getAlertMessage());
            assertNotNull(alert.getAlertLevel());
            assertEquals(AlertWorkOrder.OrderStatus.OPEN, alert.getStatus());
        }
    }

    @Test
    void testTaskTimeoutProcessing() {
        // 创建一个超时的任务
        InspectionTask timeoutTask = new InspectionTask();
        timeoutTask.setTaskCode("TIMEOUT-TASK-001");
        timeoutTask.setConfigId(deviceConfig.getId());
        timeoutTask.setTargetType(InspectionTask.TargetType.DEVICE);
        timeoutTask.setTargetId(testDevice.getId());
        timeoutTask.setStatus(InspectionTask.TaskStatus.IN_PROGRESS);
        timeoutTask.setScheduledTime(LocalDateTime.now().minusHours(3));
        timeoutTask.setStartTime(LocalDateTime.now().minusHours(3));
        timeoutTask.setTaskType(InspectionTask.TaskType.AUTO);
        timeoutTask = taskRepository.save(timeoutTask);

        // 处理超时任务
        schedulerService.handleTimeoutTasks();

        // 验证任务状态更新
        timeoutTask = taskRepository.findById(timeoutTask.getId()).orElse(null);
        assertNotNull(timeoutTask);
        assertEquals(InspectionTask.TaskStatus.FAILED, timeoutTask.getStatus());
        assertNotNull(timeoutTask.getEndTime());
    }

    @Test
    @Transactional
    void testAlertEscalation() {
        // 创建一个需要升级的告警，使用原生SQL插入以避免@CreationTimestamp自动设置时间
        LocalDateTime alertCreateTime = LocalDateTime.now().minusHours(5);

        // 使用EntityManager执行原生SQL插入
        String insertSql = """
            INSERT INTO alert_work_order
            (order_code, task_id, target_type, target_id, alert_message, alert_level, alert_type, status, create_time, update_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """;

        entityManager.createNativeQuery(insertSql)
            .setParameter(1, "TEST-ALERT-001")
            .setParameter(2, 1L)
            .setParameter(3, "DEVICE")
            .setParameter(4, testDevice.getId())
            .setParameter(5, "CPU使用率过高")
            .setParameter(6, "WARNING")
            .setParameter(7, "THRESHOLD_EXCEEDED")
            .setParameter(8, "OPEN")
            .setParameter(9, alertCreateTime)
            .setParameter(10, alertCreateTime)
            .executeUpdate();

        // 查询刚插入的告警
        AlertWorkOrder alert = alertRepository.findAll().stream()
            .filter(a -> "TEST-ALERT-001".equals(a.getOrderCode()))
            .findFirst()
            .orElse(null);

        assertNotNull(alert);
        System.out.println("设置的告警创建时间: " + alertCreateTime);
        System.out.println("数据库中的告警创建时间: " + alert.getCreateTime());
        System.out.println("告警状态: " + alert.getStatus());
        System.out.println("告警级别: " + alert.getAlertLevel());

        // 处理告警升级
        schedulerService.processAlertEscalation();

        // 验证告警级别升级
        alert = alertRepository.findById(alert.getId()).orElse(null);
        assertNotNull(alert);
        assertEquals(AlertWorkOrder.AlertLevel.CRITICAL, alert.getAlertLevel());
        assertNotNull(alert.getUpdateTime());
    }

    @Test
    void testManualInspectionTask() {
        // 创建手动巡检任务
        InspectionTask manualTask = new InspectionTask();
        manualTask.setTaskCode("MANUAL-TASK-001");
        manualTask.setConfigId(deviceConfig.getId());
        manualTask.setTargetType(InspectionTask.TargetType.DEVICE);
        manualTask.setTargetId(testDevice.getId());
        manualTask.setStatus(InspectionTask.TaskStatus.PENDING);
        manualTask.setScheduledTime(LocalDateTime.now());
        manualTask.setTaskType(InspectionTask.TaskType.MANUAL);
        // 手动巡检任务不需要设置autoInspection字段
        manualTask = taskRepository.save(manualTask);

        // 手动巡检不应该被自动执行
        schedulerService.executeAutoInspection();

        // 验证任务状态未改变
        manualTask = taskRepository.findById(manualTask.getId()).orElse(null);
        assertNotNull(manualTask);
        assertEquals(InspectionTask.TaskStatus.PENDING, manualTask.getStatus());
        assertNull(manualTask.getInspector());
    }
}
