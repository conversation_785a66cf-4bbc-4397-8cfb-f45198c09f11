package com.inspection.system.service.impl;

import com.inspection.system.dto.AlertWorkOrderDTO;
import com.inspection.system.entity.*;
import com.inspection.system.repository.*;
import com.inspection.system.service.AlertWorkOrderService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 自动巡检服务测试类
 */
@ExtendWith(MockitoExtension.class)
class AutoInspectionServiceImplTest {

    @Mock
    private InspectionTaskRepository taskRepository;
    
    @Mock
    private InspectionDetailRepository detailRepository;
    
    @Mock
    private InspectionTrackRepository trackRepository;
    
    @Mock
    private DeviceRepository deviceRepository;
    
    @Mock
    private RoomRepository roomRepository;
    
    @Mock
    private ThresholdConfigRepository thresholdRepository;
    
    @Mock
    private AlertWorkOrderService alertService;

    @InjectMocks
    private AutoInspectionServiceImpl autoInspectionService;

    private InspectionTask testTask;
    private Device testDevice;
    private Room testRoom;
    private ThresholdConfig testThreshold;

    @BeforeEach
    void setUp() {
        // 创建测试任务
        testTask = new InspectionTask();
        testTask.setId(1L);
        testTask.setTaskCode("TASK-001");
        testTask.setTargetType(InspectionTask.TargetType.DEVICE);
        testTask.setTargetId(1L);
        testTask.setStatus(InspectionTask.TaskStatus.PENDING);

        // 创建测试设备
        testDevice = new Device();
        testDevice.setId(1L);
        testDevice.setDeviceName("测试主机");
        testDevice.setDeviceType(Device.DeviceType.HOST);
        testDevice.setIpAddress("*************");

        // 创建测试机房
        testRoom = new Room();
        testRoom.setId(1L);
        testRoom.setRoomName("测试机房");
        testRoom.setLocation("A栋1楼");

        // 创建测试阈值配置
        testThreshold = new ThresholdConfig();
        testThreshold.setId(1L);
        testThreshold.setTargetType(ThresholdConfig.TargetType.DEVICE);
        testThreshold.setTargetId(1L);
        testThreshold.setItemName("CPU_USAGE");
        testThreshold.setThresholdMax(new BigDecimal("80.0"));
        testThreshold.setThresholdMin(new BigDecimal("0.0"));
        testThreshold.setAlertLevel(ThresholdConfig.AlertLevel.WARNING);
    }

    @Test
    void testExecuteInspection_DeviceInspection_Success() {
        // 准备数据
        when(deviceRepository.findById(1L)).thenReturn(Optional.of(testDevice));
        when(detailRepository.findByTaskId(1L)).thenReturn(Arrays.asList());
        when(taskRepository.save(any(InspectionTask.class))).thenReturn(testTask);
        when(trackRepository.save(any(InspectionTrack.class))).thenReturn(new InspectionTrack());
        when(detailRepository.save(any(InspectionDetail.class))).thenReturn(new InspectionDetail());
        when(thresholdRepository.findByTargetTypeAndTargetIdAndItemName(any(), any(), any()))
                .thenReturn(Optional.of(testThreshold));

        // 执行测试
        autoInspectionService.executeInspection(testTask);

        // 验证结果
        verify(taskRepository, atLeast(2)).save(any(InspectionTask.class));
        verify(trackRepository, atLeast(2)).save(any(InspectionTrack.class));
        verify(detailRepository, atLeast(5)).save(any(InspectionDetail.class)); // HOST检查5个项目
        assertEquals(InspectionTask.TaskStatus.COMPLETED, testTask.getStatus());
        assertEquals("SYSTEM", testTask.getInspector());
        assertNotNull(testTask.getStartTime());
        assertNotNull(testTask.getEndTime());
    }

    @Test
    void testExecuteInspection_RoomInspection_Success() {
        // 准备数据
        testTask.setTargetType(InspectionTask.TargetType.ROOM);
        when(roomRepository.findById(1L)).thenReturn(Optional.of(testRoom));
        when(detailRepository.findByTaskId(1L)).thenReturn(Arrays.asList());
        when(taskRepository.save(any(InspectionTask.class))).thenReturn(testTask);
        when(trackRepository.save(any(InspectionTrack.class))).thenReturn(new InspectionTrack());
        when(detailRepository.save(any(InspectionDetail.class))).thenReturn(new InspectionDetail());
        when(thresholdRepository.findByTargetTypeAndTargetIdAndItemName(any(), any(), any()))
                .thenReturn(Optional.of(testThreshold));

        // 执行测试
        autoInspectionService.executeInspection(testTask);

        // 验证结果
        verify(taskRepository, atLeast(2)).save(any(InspectionTask.class));
        verify(trackRepository, atLeast(2)).save(any(InspectionTrack.class));
        verify(detailRepository, atLeast(4)).save(any(InspectionDetail.class)); // 机房检查4个项目
        assertEquals(InspectionTask.TaskStatus.COMPLETED, testTask.getStatus());
    }

    @Test
    void testExecuteInspection_DeviceNotFound_ThrowsException() {
        // 准备数据 - 设备不存在
        when(deviceRepository.findById(1L)).thenReturn(Optional.empty());

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            autoInspectionService.executeInspection(testTask);
        });

        assertTrue(exception.getMessage().contains("设备不存在"));
        assertEquals(InspectionTask.TaskStatus.FAILED, testTask.getStatus());
    }

    @Test
    void testExecuteInspection_WithAbnormalResults_GeneratesAlert() {
        // 准备数据 - 创建异常的巡检详情
        InspectionDetail abnormalDetail = new InspectionDetail();
        abnormalDetail.setResult(InspectionDetail.DetailResult.ABNORMAL);
        
        when(deviceRepository.findById(1L)).thenReturn(Optional.of(testDevice));
        when(detailRepository.findByTaskId(1L)).thenReturn(Arrays.asList(abnormalDetail));
        when(taskRepository.save(any(InspectionTask.class))).thenReturn(testTask);
        when(trackRepository.save(any(InspectionTrack.class))).thenReturn(new InspectionTrack());
        when(detailRepository.save(any(InspectionDetail.class))).thenReturn(new InspectionDetail());
        when(thresholdRepository.findByTargetTypeAndTargetIdAndItemName(any(), any(), any()))
                .thenReturn(Optional.of(testThreshold));

        // 执行测试
        autoInspectionService.executeInspection(testTask);

        // 验证结果
        assertEquals(InspectionTask.TaskResult.ABNORMAL, testTask.getResult());
        verify(alertService, atLeast(1)).createOrder(any(AlertWorkOrderDTO.class));
    }

    @Test
    void testExecuteHostInspection() {
        // 准备数据
        when(deviceRepository.findById(1L)).thenReturn(Optional.of(testDevice));
        when(detailRepository.save(any(InspectionDetail.class))).thenReturn(new InspectionDetail());
        when(trackRepository.save(any(InspectionTrack.class))).thenReturn(new InspectionTrack());
        when(thresholdRepository.findByTargetTypeAndTargetIdAndItemName(any(), any(), any()))
                .thenReturn(Optional.of(testThreshold));

        // 执行测试
        autoInspectionService.executeHostInspection(testTask);

        // 验证结果 - 主机巡检应该检查5个项目：CPU、内存、存储、漏洞、网络连通性
        verify(detailRepository, times(5)).save(any(InspectionDetail.class));
        verify(trackRepository, times(5)).save(any(InspectionTrack.class));
    }

    @Test
    void testExecuteNetworkInspection() {
        // 准备数据
        testDevice.setDeviceType(Device.DeviceType.NETWORK);
        when(deviceRepository.findById(1L)).thenReturn(Optional.of(testDevice));
        when(detailRepository.save(any(InspectionDetail.class))).thenReturn(new InspectionDetail());
        when(trackRepository.save(any(InspectionTrack.class))).thenReturn(new InspectionTrack());
        when(thresholdRepository.findByTargetTypeAndTargetIdAndItemName(any(), any(), any()))
                .thenReturn(Optional.of(testThreshold));

        // 执行测试
        autoInspectionService.executeNetworkInspection(testTask);

        // 验证结果 - 网络设备巡检应该检查3个项目：设备状态、端口状态、流量
        verify(detailRepository, times(3)).save(any(InspectionDetail.class));
        verify(trackRepository, times(3)).save(any(InspectionTrack.class));
    }

    @Test
    void testExecuteStorageInspection() {
        // 准备数据
        testDevice.setDeviceType(Device.DeviceType.STORAGE);
        when(deviceRepository.findById(1L)).thenReturn(Optional.of(testDevice));
        when(detailRepository.save(any(InspectionDetail.class))).thenReturn(new InspectionDetail());
        when(trackRepository.save(any(InspectionTrack.class))).thenReturn(new InspectionTrack());
        when(thresholdRepository.findByTargetTypeAndTargetIdAndItemName(any(), any(), any()))
                .thenReturn(Optional.of(testThreshold));

        // 执行测试
        autoInspectionService.executeStorageInspection(testTask);

        // 验证结果 - 存储设备巡检应该检查3个项目：存储容量、RAID状态、磁盘健康
        verify(detailRepository, times(3)).save(any(InspectionDetail.class));
        verify(trackRepository, times(3)).save(any(InspectionTrack.class));
    }

    @Test
    void testExecuteRoomInspection() {
        // 准备数据
        when(roomRepository.findById(1L)).thenReturn(Optional.of(testRoom));
        when(detailRepository.save(any(InspectionDetail.class))).thenReturn(new InspectionDetail());
        when(trackRepository.save(any(InspectionTrack.class))).thenReturn(new InspectionTrack());
        when(thresholdRepository.findByTargetTypeAndTargetIdAndItemName(any(), any(), any()))
                .thenReturn(Optional.of(testThreshold));

        // 执行测试
        autoInspectionService.executeRoomInspection(testTask);

        // 验证结果 - 机房巡检应该检查4个项目：温度、湿度、电力、安防
        verify(detailRepository, times(4)).save(any(InspectionDetail.class));
        verify(trackRepository, times(4)).save(any(InspectionTrack.class));
    }
}
