package com.inspection.system.service.impl;

import com.inspection.system.entity.*;
import com.inspection.system.repository.*;
import com.inspection.system.service.SchedulerService;
import com.inspection.system.service.AutoInspectionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 定时任务调度服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class SchedulerServiceImpl implements SchedulerService {

    
    
    private final InspectionConfigRepository configRepository;
    private final InspectionTaskRepository taskRepository;
    private final AlertWorkOrderRepository alertRepository;
    private final AutoInspectionService autoInspectionService;

    /**
     * 每小时生成周期性巡检任务
     */
    @Scheduled(cron = "0 0 * * * ?")
    @Override
    public void generatePeriodicInspectionTasks() {
        log.info("开始生成周期性巡检任务...");
        
        List<InspectionConfig> activeConfigs = configRepository.findByStatus(InspectionConfig.ConfigStatus.ACTIVE);
        LocalDateTime now = LocalDateTime.now();
        
        for (InspectionConfig config : activeConfigs) {
            try {
                if (shouldGenerateTask(config, now)) {
                    generateTaskForConfig(config, now);
                }
            } catch (Exception e) {
                log.error("生成配置{}的巡检任务失败: {}", config.getId(), e.getMessage());
            }
        }
        
        log.info("周期性巡检任务生成完成");
    }

    /**
     * 每10分钟执行一次自动巡检
     */
    @Scheduled(cron = "0 */10 * * * ?")
    @Override
    public void executeAutoInspection() {
        log.info("开始执行自动巡检...");
        
        List<InspectionTask> pendingTasks = taskRepository.findPendingAutoTasks(LocalDateTime.now());
        
        for (InspectionTask task : pendingTasks) {
            try {
                autoInspectionService.executeInspection(task);
            } catch (Exception e) {
                log.error("执行任务{}自动巡检失败: {}", task.getId(), e.getMessage());
                // 标记任务为失败
                task.setStatus(InspectionTask.TaskStatus.FAILED);
                task.setEndTime(LocalDateTime.now());
                taskRepository.save(task);
            }
        }
        
        log.info("自动巡检执行完成");
    }

    /**
     * 每30分钟处理超时任务
     */
    @Scheduled(cron = "0 */30 * * * ?")
    @Override
    public void handleTimeoutTasks() {
        log.info("开始处理超时任务...");
        
        LocalDateTime timeoutThreshold = LocalDateTime.now().minusHours(2);
        List<InspectionTask> timeoutTasks = taskRepository.findTimeoutTasks(timeoutThreshold);
        
        for (InspectionTask task : timeoutTasks) {
            task.setStatus(InspectionTask.TaskStatus.FAILED);
            task.setEndTime(LocalDateTime.now());
            taskRepository.save(task);
            log.warn("任务{}执行超时，已标记为失败", task.getId());
        }
        
        log.info("超时任务处理完成，共处理{}个任务", timeoutTasks.size());
    }

    /**
     * 每小时处理告警升级
     */
    @Scheduled(cron = "0 0 * * * ?")
    @Override
    public void processAlertEscalation() {
        log.info("开始处理告警升级...");

        LocalDateTime escalationThreshold = LocalDateTime.now().minusHours(4);
        log.info("告警升级阈值时间: {}", escalationThreshold);

        List<AlertWorkOrder> pendingAlerts = alertRepository.findPendingAlertsForEscalation(escalationThreshold);
        log.info("查询到{}个待升级告警", pendingAlerts.size());

        for (AlertWorkOrder alert : pendingAlerts) {
            try {
                log.info("处理告警升级: ID={}, 当前级别={}, 创建时间={}",
                    alert.getId(), alert.getAlertLevel(), alert.getCreateTime());
                escalateAlert(alert);
            } catch (Exception e) {
                log.error("告警{}升级失败: {}", alert.getId(), e.getMessage());
            }
        }

        log.info("告警升级处理完成，共处理{}个告警", pendingAlerts.size());
    }

    /**
     * 判断是否应该生成任务
     */
    private boolean shouldGenerateTask(InspectionConfig config, LocalDateTime now) {
        // 检查是否已存在相同时间段的任务
        LocalDateTime startOfHour = now.withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endOfHour = startOfHour.plusHours(1);
        
        boolean taskExists = taskRepository.existsByConfigIdAndScheduledTimeBetween(
                config.getId(), startOfHour, endOfHour);
        
        if (taskExists) {
            return false;
        }
        
        // 根据频率类型判断
        return switch (config.getFrequencyType()) {
            case DAILY -> true; // 每小时检查，每天都生成
            case WEEKLY -> now.getDayOfWeek().getValue() == 1; // 周一生成
            case MONTHLY -> now.getDayOfMonth() == 1; // 每月1号生成
            case QUARTERLY -> now.getDayOfMonth() == 1 && (now.getMonthValue() - 1) % 3 == 0; // 季度首月1号生成
        };
    }

    /**
     * 为配置生成任务
     */
    private void generateTaskForConfig(InspectionConfig config, LocalDateTime now) {
        InspectionTask task = new InspectionTask();
        task.setTaskCode(generateTaskCode());
        task.setConfigId(config.getId());
        task.setTargetType(InspectionTask.TargetType.valueOf(config.getTargetType().name()));
        task.setTargetId(config.getTargetId());
        task.setTaskType(config.getAutoInspection() ? InspectionTask.TaskType.AUTO : InspectionTask.TaskType.MANUAL);
        task.setStatus(InspectionTask.TaskStatus.PENDING);
        task.setPriority(InspectionTask.TaskPriority.NORMAL);
        task.setScheduledTime(now); // 立即执行
        
        taskRepository.save(task);
        log.info("为配置{}生成巡检任务: {}", config.getId(), task.getTaskCode());
    }

    /**
     * 告警升级
     */
    private void escalateAlert(AlertWorkOrder alert) {
        AlertWorkOrder.AlertLevel currentLevel = alert.getAlertLevel();
        AlertWorkOrder.AlertLevel newLevel = switch (currentLevel) {
            case INFO -> AlertWorkOrder.AlertLevel.WARNING;
            case WARNING -> AlertWorkOrder.AlertLevel.CRITICAL;
            case CRITICAL -> AlertWorkOrder.AlertLevel.CRITICAL; // 已是最高级别
        };
        
        if (newLevel != currentLevel) {
            alert.setAlertLevel(newLevel);
            alert.setUpdateTime(LocalDateTime.now());
            alertRepository.save(alert);
            log.info("告警{}级别从{}升级为{}", alert.getId(), currentLevel, newLevel);
        }
    }

    /**
     * 生成任务编码
     */
    private String generateTaskCode() {
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        long count = taskRepository.count() + 1;
        return String.format("TASK%s%03d", dateStr, count);
    }
}
