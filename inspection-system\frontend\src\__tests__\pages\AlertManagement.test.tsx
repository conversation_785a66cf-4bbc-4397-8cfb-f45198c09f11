import React from 'react';
import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';

// Create a simple mock component to avoid Form.useForm issues
const MockAlertManagement = () => (
  <div data-testid="alert-management">
    <div data-testid="card" data-title="告警管理">
      <div data-testid="form">
        <input data-testid="input" placeholder="搜索告警" />
        <select data-testid="select">
          <option value="">选择级别</option>
        </select>
        <button data-testid="button">搜索</button>
      </div>
      <div data-testid="table">
        <div data-testid="table-columns">5 columns</div>
        <div data-testid="table-rows">0 rows</div>
      </div>
    </div>
  </div>
);

const renderWithRouter = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

describe('AlertManagement Component', () => {
  it('renders without crashing', () => {
    renderWithRouter(<MockAlertManagement />);
    expect(screen.getByTestId('alert-management')).toBeInTheDocument();
  });

  it('displays alert management title', () => {
    renderWithRouter(<MockAlertManagement />);
    const card = screen.getByTestId('card');
    expect(card).toHaveAttribute('data-title', '告警管理');
  });

  it('renders search form', () => {
    renderWithRouter(<MockAlertManagement />);
    expect(screen.getByTestId('form')).toBeInTheDocument();
    expect(screen.getByTestId('input')).toBeInTheDocument();
    expect(screen.getByTestId('select')).toBeInTheDocument();
    expect(screen.getByTestId('button')).toBeInTheDocument();
  });

  it('renders alerts table', () => {
    renderWithRouter(<MockAlertManagement />);
    expect(screen.getByTestId('table')).toBeInTheDocument();
    expect(screen.getByTestId('table-columns')).toBeInTheDocument();
    expect(screen.getByTestId('table-rows')).toBeInTheDocument();
  });
});

