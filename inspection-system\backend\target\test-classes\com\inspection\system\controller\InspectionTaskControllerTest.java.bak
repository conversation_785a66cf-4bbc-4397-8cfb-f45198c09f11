package com.inspection.system.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.inspection.system.dto.InspectionTaskDTO;
import com.inspection.system.entity.InspectionTask;
import com.inspection.system.service.InspectionTaskService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 巡检任务控制器测试类
 */
@WebMvcTest(InspectionTaskController.class)
class InspectionTaskControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private InspectionTaskService taskService;

    @Autowired
    private ObjectMapper objectMapper;

    private InspectionTaskDTO testTaskDTO;
    private InspectionTask testTask;

    @BeforeEach
    void setUp() {
        testTaskDTO = new InspectionTaskDTO();
        testTaskDTO.setId(1L);
        testTaskDTO.setTaskCode("TASK001");
        testTaskDTO.setTaskName("测试巡检任务");
        testTaskDTO.setStatus(InspectionTask.TaskStatus.PENDING);
        testTaskDTO.setTaskType(InspectionTask.TaskType.AUTO);
        testTaskDTO.setPriority(InspectionTask.TaskPriority.HIGH);
        testTaskDTO.setScheduledTime(LocalDateTime.now());
        testTaskDTO.setConfigId(1L);

        testTask = new InspectionTask();
        testTask.setId(1L);
        testTask.setTaskCode("TASK001");
        testTask.setTaskName("测试巡检任务");
        testTask.setStatus(InspectionTask.TaskStatus.PENDING);
        testTask.setTaskType(InspectionTask.TaskType.AUTO);
        testTask.setPriority(InspectionTask.TaskPriority.HIGH);
        testTask.setScheduledTime(LocalDateTime.now());
    }

    @Test
    void testGetTasks() throws Exception {
        // 准备数据
        List<InspectionTaskDTO> tasks = Arrays.asList(testTaskDTO);
        Page<InspectionTaskDTO> page = new PageImpl<>(tasks, PageRequest.of(0, 10), 1);
        
        when(taskService.getTasks(any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(page);

        // 执行测试
        mockMvc.perform(get("/tasks")
                        .param("page", "0")
                        .param("size", "10")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.content").isArray())
                .andExpect(jsonPath("$.data.content[0].taskCode").value("TASK001"))
                .andExpect(jsonPath("$.data.totalElements").value(1));

        // 验证服务调用
        verify(taskService).getTasks(any(), any(), any(), any(), any(), any(), any());
    }

    @Test
    void testGetTaskById() throws Exception {
        // 准备数据
        when(taskService.getTaskById(1L)).thenReturn(testTaskDTO);

        // 执行测试
        mockMvc.perform(get("/tasks/1")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.taskCode").value("TASK001"))
                .andExpect(jsonPath("$.data.taskName").value("测试巡检任务"));

        // 验证服务调用
        verify(taskService).getTaskById(1L);
    }

    @Test
    void testCreateTask() throws Exception {
        // 准备数据
        when(taskService.createTask(any(InspectionTaskDTO.class))).thenReturn(testTaskDTO);

        // 执行测试
        mockMvc.perform(post("/tasks")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testTaskDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.taskCode").value("TASK001"));

        // 验证服务调用
        verify(taskService).createTask(any(InspectionTaskDTO.class));
    }

    @Test
    void testUpdateTask() throws Exception {
        // 准备数据
        testTaskDTO.setTaskName("更新后的任务名称");
        when(taskService.updateTask(eq(1L), any(InspectionTaskDTO.class))).thenReturn(testTaskDTO);

        // 执行测试
        mockMvc.perform(put("/tasks/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testTaskDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.taskName").value("更新后的任务名称"));

        // 验证服务调用
        verify(taskService).updateTask(eq(1L), any(InspectionTaskDTO.class));
    }

    @Test
    void testDeleteTask() throws Exception {
        // 准备数据
        doNothing().when(taskService).deleteTask(1L);

        // 执行测试
        mockMvc.perform(delete("/tasks/1")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        // 验证服务调用
        verify(taskService).deleteTask(1L);
    }

    @Test
    void testExecuteTask() throws Exception {
        // 准备数据
        doNothing().when(taskService).executeTask(1L);

        // 执行测试
        mockMvc.perform(post("/tasks/1/execute")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        // 验证服务调用
        verify(taskService).executeTask(1L);
    }

    @Test
    void testBatchDeleteTasks() throws Exception {
        // 准备数据
        List<Long> taskIds = Arrays.asList(1L, 2L, 3L);
        doNothing().when(taskService).batchDeleteTasks(taskIds);

        // 执行测试
        mockMvc.perform(delete("/tasks/batch")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(taskIds)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        // 验证服务调用
        verify(taskService).batchDeleteTasks(taskIds);
    }

    @Test
    void testGetTasksWithFilters() throws Exception {
        // 准备数据
        List<InspectionTaskDTO> tasks = Arrays.asList(testTaskDTO);
        Page<InspectionTaskDTO> page = new PageImpl<>(tasks, PageRequest.of(0, 10), 1);
        
        when(taskService.getTasks(any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(page);

        // 执行测试 - 带过滤条件
        mockMvc.perform(get("/tasks")
                        .param("page", "0")
                        .param("size", "10")
                        .param("keyword", "测试")
                        .param("status", "PENDING")
                        .param("taskType", "AUTO")
                        .param("priority", "HIGH")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.content").isArray());

        // 验证服务调用
        verify(taskService).getTasks(any(), eq("测试"), eq(InspectionTask.TaskStatus.PENDING), 
                eq(InspectionTask.TaskType.AUTO), eq(InspectionTask.TaskPriority.HIGH), any(), any());
    }

    @Test
    void testCreateTaskWithValidation() throws Exception {
        // 准备数据 - 缺少必填字段
        InspectionTaskDTO invalidTask = new InspectionTaskDTO();
        invalidTask.setTaskName(""); // 空任务名称

        // 执行测试
        mockMvc.perform(post("/tasks")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(invalidTask)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testGetTaskByIdNotFound() throws Exception {
        // 准备数据
        when(taskService.getTaskById(999L)).thenThrow(new RuntimeException("任务不存在"));

        // 执行测试
        mockMvc.perform(get("/tasks/999")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isInternalServerError());

        // 验证服务调用
        verify(taskService).getTaskById(999L);
    }

    @Test
    void testUpdateTaskNotFound() throws Exception {
        // 准备数据
        when(taskService.updateTask(eq(999L), any(InspectionTaskDTO.class)))
                .thenThrow(new RuntimeException("任务不存在"));

        // 执行测试
        mockMvc.perform(put("/tasks/999")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testTaskDTO)))
                .andExpect(status().isInternalServerError());

        // 验证服务调用
        verify(taskService).updateTask(eq(999L), any(InspectionTaskDTO.class));
    }

    @Test
    void testDeleteTaskNotFound() throws Exception {
        // 准备数据
        doThrow(new RuntimeException("任务不存在")).when(taskService).deleteTask(999L);

        // 执行测试
        mockMvc.perform(delete("/tasks/999")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isInternalServerError());

        // 验证服务调用
        verify(taskService).deleteTask(999L);
    }
}