/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body, #root {
  height: 100%;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-attachment: fixed;
  color: #2c3e50;
  line-height: 1.6;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 页面布局 */
.app-layout {
  min-height: 100vh;
  height: 100vh;
  background: transparent;
  overflow: hidden;
}

.app-layout .ant-layout {
  min-height: 100vh;
  height: 100vh;
}

.app-layout .ant-layout-content {
  min-height: calc(100vh - 64px);
  height: calc(100vh - 64px);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 侧边栏样式 */
.app-sider {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px) !important;
  border-right: 1px solid rgba(255, 255, 255, 0.2) !important;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1) !important;
}

.app-sider .ant-layout-sider-children {
  height: 100vh;
  overflow-y: auto;
}

/* 页面容器 */
.page-container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0;
  min-height: calc(100vh - 112px);
}

/* 页面内容区域 */
.fade-in {
  animation: fadeInUp 0.6s ease-out;
  min-height: calc(100vh - 112px);
  width: 100%;
}

/* 确保表格容器有正确的高度 */
.ant-table-wrapper {
  min-height: 400px;
}

/* 卡片容器最小高度 */
.modern-card {
  min-height: 120px;
}

/* 统计卡片固定高度 */
.stat-card {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 图表卡片固定高度 */
.modern-card .echarts-for-react {
  min-height: 300px !important;
  height: 300px !important;
}

.app-header {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px) !important;
  color: #2c3e50 !important;
  padding: 0 24px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
  height: 64px !important;
  line-height: 64px !important;
}

.app-logo {
  font-size: 20px;
  font-weight: bold;
  background: linear-gradient(45deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.app-sider {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
}

.app-content {
  padding: 24px;
  background: transparent;
  min-height: calc(100vh - 64px);
  position: relative;
}

/* 页面标题 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(45deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

/* 卡片样式 */
.stat-card {
  text-align: center;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  position: relative;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.stat-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  border-color: rgba(102, 126, 234, 0.3);
}

.stat-number {
  font-size: 42px;
  font-weight: 800;
  background: linear-gradient(45deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 12px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-label {
  color: #64748b;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

/* 图表容器 */
.chart-container {
  height: 300px;
  min-height: 300px;
  width: 100%;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

/* 确保ECharts容器不塌陷 */
.echarts-for-react {
  min-height: 300px !important;
  height: 300px !important;
}

/* 图表卡片最小高度 */
.ant-card .echarts-for-react {
  min-height: 300px !important;
}

/* 状态标签 */
.status-tag {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.status-pending {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(255, 193, 7, 0.1));
  color: #f59e0b;
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

.status-in-progress {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(59, 130, 246, 0.1));
  color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.status-completed {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.2), rgba(34, 197, 94, 0.1));
  color: #22c55e;
  box-shadow: 0 2px 8px rgba(34, 197, 94, 0.3);
}

.status-failed {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.1));
  color: #ef4444;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

/* 优先级标签 */
.priority-high {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.1));
  color: #ef4444;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.priority-normal {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.2), rgba(34, 197, 94, 0.1));
  color: #22c55e;
  box-shadow: 0 2px 8px rgba(34, 197, 94, 0.3);
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.priority-low {
  background: linear-gradient(135deg, rgba(148, 163, 184, 0.2), rgba(148, 163, 184, 0.1));
  color: #64748b;
  box-shadow: 0 2px 8px rgba(148, 163, 184, 0.3);
  border: 1px solid rgba(148, 163, 184, 0.3);
}

/* 告警级别标签 */
.alert-critical {
  background: linear-gradient(135deg, rgba(220, 38, 127, 0.2), rgba(220, 38, 127, 0.1));
  color: #dc2626;
  box-shadow: 0 2px 8px rgba(220, 38, 127, 0.4);
  border: 1px solid rgba(220, 38, 127, 0.3);
  animation: pulse 2s infinite;
}

.alert-warning {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.2), rgba(245, 158, 11, 0.1));
  color: #f59e0b;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.alert-info {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(59, 130, 246, 0.1));
  color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
  border: 1px solid rgba(59, 130, 246, 0.3);
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* 页面加载动画 */
.fade-in {
  animation: fadeInUp 0.6s ease-out;
}

.slide-in {
  animation: slideInLeft 0.5s ease-out;
}

/* 加载骨架屏效果 */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* 悬浮效果增强 - 优化尺寸 */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 6px 12px;
  font-size: 14px;
  height: 32px;
  border-radius: 6px;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

/* 现代化滚动条 */
.modern-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.modern-scrollbar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.modern-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 3px;
}

.modern-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8, #6a4190);
}

/* 巡检结果样式 */
.result-item {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.result-item:hover {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.result-name {
  font-weight: 600;
  font-size: 14px;
  color: #1f2937;
  margin-bottom: 4px;
}

.result-desc {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 8px;
}

.result-value {
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 8px;
}

.result-normal {
  color: #22c55e;
}

.result-warning {
  color: #f59e0b;
}

.result-error {
  color: #ef4444;
}

.result-status {
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-layout .ant-layout {
    margin-left: 0 !important;
  }

  .app-sider {
    position: fixed !important;
    left: -200px !important;
    transition: left 0.3s ease !important;
  }

  .app-sider.ant-layout-sider-collapsed {
    left: -80px !important;
  }

  .app-header {
    left: 0 !important;
  }

  .app-content {
    padding: 16px !important;
    margin-top: 64px !important;
  }

  .page-container {
    max-width: 100% !important;
    padding: 0 !important;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    padding: 16px !important;
  }

  .stat-number {
    font-size: 24px;
  }

  .stat-card {
    height: 100px;
  }
}

/* 工具类 */
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.mb-16 {
  margin-bottom: 16px;
}

.mb-24 {
  margin-bottom: 24px;
}

.mt-16 {
  margin-top: 16px;
}

.mt-24 {
  margin-top: 24px;
}

/* 现代化卡片样式 */
.modern-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.modern-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* 现代化按钮样式 - 优化尺寸 */
.modern-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  border-radius: 8px !important;
  color: white !important;
  font-weight: 500 !important;
  padding: 6px 16px !important;
  font-size: 14px !important;
  height: 36px !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3) !important;
}

.modern-button:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.5) !important;
  color: white !important;
}

.modern-button:focus {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3) !important;
}

.modern-button:active {
  transform: translateY(0) !important;
  box-shadow: 0 2px 6px rgba(102, 126, 234, 0.4) !important;
}

/* 毛玻璃效果 */
.glass-effect {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  border-radius: 16px;
}

/* 渐变文字 */
.gradient-text {
  background: linear-gradient(45deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Ant Design 组件样式覆盖 */
.ant-card {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px) !important;
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  transition: all 0.3s ease !important;
  margin-bottom: 16px !important;
  min-height: 120px !important;
}

.ant-card:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15) !important;
}

.ant-card-head {
  background: transparent !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
  padding: 16px 24px !important;
  min-height: 60px !important;
}

.ant-card-body {
  padding: 24px !important;
  min-height: 60px !important;
}

/* 搜索表单卡片 */
.ant-card.modern-card {
  min-height: 140px !important;
}

/* 表格卡片 */
.ant-card:has(.ant-table) {
  min-height: 500px !important;
}

/* 统计卡片特殊样式 */
.stat-card.ant-card {
  min-height: 140px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 侧边栏菜单样式 */
.ant-layout-sider {
  background: rgba(255, 255, 255, 0.95) !important;
}

.ant-menu {
  background: transparent !important;
  border-right: none !important;
}

.ant-menu-item {
  margin: 4px 8px !important;
  border-radius: 8px !important;
  height: 40px !important;
  line-height: 40px !important;
}

.ant-menu-item:hover {
  background: rgba(102, 126, 234, 0.1) !important;
  color: #667eea !important;
}

.ant-menu-item-selected {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
}

.ant-menu-item-selected:hover {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
}

/* 表格样式优化 */
.ant-table {
  background: transparent !important;
  font-size: 14px !important;
}

.ant-table-thead > tr > th {
  background: rgba(255, 255, 255, 0.9) !important;
  border-bottom: 2px solid rgba(102, 126, 234, 0.2) !important;
  font-weight: 600 !important;
  color: #2c3e50 !important;
  padding: 16px 12px !important;
  font-size: 14px !important;
}

.ant-table-tbody > tr > td {
  border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
  padding: 12px !important;
  font-size: 14px !important;
  vertical-align: middle !important;
}

.ant-table-tbody > tr:hover > td {
  background: rgba(102, 126, 234, 0.08) !important;
}

.ant-table-tbody > tr:nth-child(even) > td {
  background: rgba(255, 255, 255, 0.3) !important;
}

.ant-table-tbody > tr:nth-child(even):hover > td {
  background: rgba(102, 126, 234, 0.08) !important;
}

/* 表格内按钮样式 */
.ant-table .ant-btn {
  height: 28px !important;
  padding: 4px 8px !important;
  font-size: 12px !important;
  border-radius: 6px !important;
  margin: 0 2px !important;
  font-weight: 500 !important;
}

.ant-table .ant-btn-link {
  height: 28px !important;
  padding: 4px 8px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
}

.ant-table .ant-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  color: white !important;
}

.ant-table .ant-btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4) !important;
}

/* 全局按钮样式统一 */
.ant-btn {
  border-radius: 8px !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  height: 36px !important;
  padding: 4px 16px !important;
  font-size: 14px !important;
}

.ant-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  color: white !important;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3) !important;
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4) !important;
  color: white !important;
}

.ant-btn-default {
  border: 1px solid #d9d9d9 !important;
  background: rgba(255, 255, 255, 0.9) !important;
  color: #595959 !important;
}

.ant-btn-default:hover {
  border-color: #667eea !important;
  color: #667eea !important;
  background: rgba(255, 255, 255, 0.95) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2) !important;
}

.ant-btn-danger {
  background: linear-gradient(135deg, #ff4d4f 0%, #cf1322 100%) !important;
  border: none !important;
  color: white !important;
}

.ant-btn-danger:hover {
  background: linear-gradient(135deg, #ff7875 0%, #a8071a 100%) !important;
  transform: translateY(-1px) !important;
  color: white !important;
}

/* 链接按钮样式 */
.ant-btn-link {
  color: #667eea !important;
  font-weight: 500 !important;
  padding: 4px 8px !important;
  height: 28px !important;
}

.ant-btn-link:hover {
  color: #5a6fd8 !important;
  background: rgba(102, 126, 234, 0.1) !important;
}

/* 小尺寸按钮 */
.ant-btn-sm {
  height: 28px !important;
  padding: 2px 8px !important;
  font-size: 12px !important;
}

/* 大尺寸按钮 */
.ant-btn-lg {
  height: 44px !important;
  padding: 8px 24px !important;
  font-size: 16px !important;
}

/* 页面特殊样式 */
.mb-16 {
  margin-bottom: 16px !important;
}

.mb-24 {
  margin-bottom: 24px !important;
}

/* 搜索表单样式 */
.ant-form-inline .ant-form-item {
  margin-bottom: 16px !important;
}

.ant-form-inline .ant-row {
  width: 100% !important;
}

/* 批量操作栏样式 */
.batch-operations {
  background: rgba(102, 126, 234, 0.1) !important;
  border: 1px solid rgba(102, 126, 234, 0.2) !important;
  border-radius: 12px !important;
  padding: 16px !important;
  margin-bottom: 16px !important;
}

/* 表格行选择样式 */
.ant-table-tbody > tr.ant-table-row-selected > td {
  background: rgba(102, 126, 234, 0.1) !important;
}

.ant-table-tbody > tr.ant-table-row-selected:hover > td {
  background: rgba(102, 126, 234, 0.15) !important;
}

/* 分页样式 */
.ant-pagination {
  margin-top: 24px !important;
  text-align: center !important;
}

.ant-pagination .ant-pagination-item {
  border-radius: 8px !important;
  border: 1px solid #d9d9d9 !important;
}

.ant-pagination .ant-pagination-item-active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border-color: #667eea !important;
}

.ant-pagination .ant-pagination-item-active a {
  color: white !important;
}

/* 空状态样式 */
.ant-empty {
  padding: 40px 0 !important;
}

.ant-empty-description {
  color: #999 !important;
  font-size: 14px !important;
}

/* 按钮尺寸统一 */
.ant-btn {
  height: 32px !important;
  padding: 4px 12px !important;
  font-size: 14px !important;
  border-radius: 6px !important;
}

.ant-btn-sm {
  height: 24px !important;
  padding: 0 8px !important;
  font-size: 12px !important;
}

.ant-btn-lg {
  height: 40px !important;
  padding: 6px 16px !important;
  font-size: 16px !important;
}

/* 表格内按钮优化 */
.ant-table .ant-btn {
  height: 28px !important;
  padding: 2px 8px !important;
  font-size: 12px !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* 简洁首页样式 */
.home-page {
  min-height: 100vh;
  background: #fafbfc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 简洁头部 */
.clean-header {
  background: #ffffff;
  border-bottom: 1px solid #e8eaed;
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.brand {
  display: flex;
  align-items: center;
  gap: 12px;
}

.brand-logo {
  font-size: 24px;
}

.brand-name {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
}

.nav-menu {
  display: flex;
  gap: 32px;
}

.nav-item {
  color: #5f6368;
  text-decoration: none;
  font-weight: 500;
  padding: 8px 0;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.nav-item:hover {
  color: #1a73e8;
}

.nav-item.active {
  color: #1a73e8;
  border-bottom-color: #1a73e8;
}

/* 主要内容区域 */
.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 欢迎区域 */
.welcome-section {
  padding: 80px 0;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.welcome-container {
  max-width: 500px;
}

.welcome-badge {
  display: inline-block;
  background: #e8f0fe;
  color: #1a73e8;
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 24px;
}

.welcome-title {
  font-size: 48px !important;
  font-weight: 700 !important;
  color: #1a1a1a !important;
  line-height: 1.2 !important;
  margin-bottom: 24px !important;
}

.title-highlight {
  color: #1a73e8 !important;
}

.welcome-description {
  font-size: 18px !important;
  color: #5f6368 !important;
  line-height: 1.6 !important;
  margin-bottom: 32px !important;
}

.welcome-actions {
  display: flex;
  gap: 16px;
}

.primary-btn {
  background: #1a73e8 !important;
  border-color: #1a73e8 !important;
  border-radius: 8px !important;
  height: 48px !important;
  padding: 0 32px !important;
  font-weight: 500 !important;
}

.secondary-btn {
  border-color: #dadce0 !important;
  color: #1a73e8 !important;
  border-radius: 8px !important;
  height: 48px !important;
  padding: 0 32px !important;
  font-weight: 500 !important;
}

.secondary-btn:hover {
  border-color: #1a73e8 !important;
  color: #1a73e8 !important;
}

/* 欢迎区域视觉元素 */
.welcome-visual {
  position: relative;
  height: 400px;
}

.visual-card {
  position: absolute;
  background: #ffffff;
  border: 1px solid #e8eaed;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  animation: float 6s ease-in-out infinite;
}

.visual-card .card-icon {
  font-size: 24px;
}

.visual-card .card-label {
  color: #5f6368;
  font-weight: 500;
  font-size: 14px;
}

.card-1 {
  top: 20px;
  left: 50px;
  animation-delay: 0s;
}

.card-2 {
  top: 120px;
  right: 80px;
  animation-delay: 2s;
}

.card-3 {
  bottom: 60px;
  left: 20px;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

/* 功能卡片区域 */
.features-section {
  padding: 80px 0;
  background: #ffffff;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-title {
  font-size: 36px !important;
  font-weight: 700 !important;
  color: #1a1a1a !important;
  margin-bottom: 16px !important;
}

.section-desc {
  font-size: 16px !important;
  color: #5f6368 !important;
  line-height: 1.6 !important;
  max-width: 600px;
  margin: 0 auto !important;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.feature-card {
  background: #ffffff;
  border: 1px solid #e8eaed;
  border-radius: 12px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.feature-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.card-title {
  font-size: 20px !important;
  font-weight: 600 !important;
  color: #1a1a1a !important;
  margin: 0 !important;
}

.card-content {
  flex: 1;
}

.card-desc {
  color: #5f6368 !important;
  line-height: 1.5 !important;
  margin-bottom: 16px !important;
}

.card-features {
  list-style: none;
  padding: 0;
  margin: 0 0 20px 0;
}

.card-features li {
  padding: 4px 0;
  color: #5f6368;
  font-size: 14px;
  position: relative;
  padding-left: 16px;
}

.card-features li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: #1a73e8;
  font-weight: bold;
}

.card-footer {
  margin-top: auto;
}

.card-btn {
  color: #1a73e8 !important;
  font-weight: 500 !important;
  padding: 0 !important;
  height: auto !important;
}

.card-btn:hover {
  color: #1557b0 !important;
}

/* 优势特点区域 */
.advantages-section {
  padding: 80px 0;
  background: #fafbfc;
}

.advantages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.advantage-item {
  display: flex;
  gap: 16px;
  padding: 24px;
  background: #ffffff;
  border: 1px solid #e8eaed;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.advantage-item:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.advantage-icon {
  font-size: 32px;
  flex-shrink: 0;
}

.advantage-content h4 {
  font-size: 18px !important;
  font-weight: 600 !important;
  color: #1a1a1a !important;
  margin-bottom: 8px !important;
}

.advantage-content p {
  color: #5f6368 !important;
  line-height: 1.5 !important;
  margin: 0 !important;
  font-size: 14px !important;
}

/* 简洁底部 */
.clean-footer {
  background: #ffffff;
  border-top: 1px solid #e8eaed;
  padding: 60px 0 0;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  padding-bottom: 40px;
}

.footer-brand {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
}

.logo-icon {
  font-size: 20px;
}

.footer-desc {
  color: #5f6368 !important;
  line-height: 1.5 !important;
  font-size: 14px !important;
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 40px;
}

.link-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.link-group h5 {
  color: #1a1a1a !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  margin-bottom: 8px !important;
}

.link-group a {
  color: #5f6368;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.2s ease;
}

.link-group a:hover {
  color: #1a73e8;
}

.footer-bottom {
  border-top: 1px solid #e8eaed;
  padding: 24px 0;
  text-align: center;
  max-width: 1200px;
  margin: 0 auto;
}

.footer-bottom p {
  color: #5f6368 !important;
  margin: 0 !important;
  font-size: 14px !important;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .welcome-section {
    grid-template-columns: 1fr;
    gap: 60px;
    text-align: center;
  }
  
  .welcome-title {
    font-size: 40px !important;
  }
  
  .features-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
  
  .footer-container {
    grid-template-columns: 1fr;
    gap: 40px;
  }
}

@media (max-width: 768px) {
  .header-container {
    padding: 0 16px;
  }
  
  .nav-menu {
    display: none;
  }
  
  .main-content {
    padding: 0 16px;
  }
  
  .welcome-section {
    padding: 60px 0;
  }
  
  .welcome-title {
    font-size: 32px !important;
  }
  
  .welcome-actions {
    flex-direction: column;
    gap: 12px;
  }
  
  .features-section,
  .advantages-section {
    padding: 60px 0;
  }
  
  .section-title {
    font-size: 28px !important;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .advantages-grid {
    grid-template-columns: 1fr;
  }
  
  .clean-footer {
    padding: 40px 0 0;
  }
  
  .footer-container {
    padding: 0 16px;
  }
  
  .footer-links {
    grid-template-columns: 1fr;
    gap: 24px;
  }
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px 24px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-height: 80px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
}

/* 统计卡片样式 */
.stats-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  height: 140px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.stats-card h3 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #666;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stats-card .number {
  font-size: 36px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.stats-card .number .anticon {
  font-size: 24px;
  color: #667eea;
}

/* Tag标签样式优化 */
.ant-tag {
  border-radius: 6px !important;
  padding: 2px 8px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  border: none !important;
}

.ant-tag-green {
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%) !important;
  color: white !important;
}

.ant-tag-orange {
  background: linear-gradient(135deg, #fa8c16 0%, #d46b08 100%) !important;
  color: white !important;
}

.ant-tag-red {
  background: linear-gradient(135deg, #ff4d4f 0%, #cf1322 100%) !important;
  color: white !important;
}

.ant-tag-blue {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%) !important;
  color: white !important;
}

.ant-tag-default {
  background: linear-gradient(135deg, #d9d9d9 0%, #bfbfbf 100%) !important;
  color: #595959 !important;
}

/* 表单样式优化 */
.ant-form-item-label > label {
  font-weight: 500 !important;
  color: #2c3e50 !important;
}

.ant-input {
  border-radius: 8px !important;
  border: 1px solid #e1e5e9 !important;
  padding: 8px 12px !important;
  font-size: 14px !important;
  transition: all 0.3s ease !important;
}

.ant-input:hover {
  border-color: #667eea !important;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1) !important;
}

.ant-input:focus {
  border-color: #667eea !important;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2) !important;
}

.ant-select .ant-select-selector {
  border-radius: 8px !important;
  border: 1px solid #e1e5e9 !important;
  padding: 4px 8px !important;
  font-size: 14px !important;
}

.ant-select:hover .ant-select-selector {
  border-color: #667eea !important;
}

.ant-select-focused .ant-select-selector {
  border-color: #667eea !important;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2) !important;
}

.ant-picker {
  border-radius: 8px !important;
  border: 1px solid #e1e5e9 !important;
  padding: 8px 12px !important;
  font-size: 14px !important;
}

.ant-picker:hover {
  border-color: #667eea !important;
}

.ant-picker-focused {
  border-color: #667eea !important;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2) !important;
}

.page-title {
  font-size: 32px !important;
  font-weight: bold !important;
  color: #333 !important;
  margin-bottom: 16px !important;
}

.page-subtitle {
  font-size: 16px !important;
  color: #666 !important;
  max-width: 600px;
  margin: 0 auto !important;
  line-height: 1.6 !important;
}

.feature-grid {
  margin-bottom: 48px;
}

.feature-card {
  height: 100%;
  border-radius: 12px !important;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0,0,0,0.15);
}

.card-header {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin: -1px -1px 0 -1px;
}

.card-icon {
  font-size: 48px;
  font-weight: bold;
}

.card-content {
  padding: 24px;
}

.card-title {
  font-size: 20px !important;
  font-weight: bold !important;
  color: #333 !important;
  margin-bottom: 12px !important;
}

.card-description {
  color: #666 !important;
  line-height: 1.6 !important;
  margin-bottom: 20px !important;
}

.card-features {
  list-style: none;
  margin-bottom: 20px;
  padding: 0;
}

.card-features li {
  padding: 4px 0;
  color: #666;
  font-size: 14px;
  position: relative;
  padding-left: 20px;
}

.card-features li:before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #52c41a;
  font-weight: bold;
}

.info-section {
  background: white;
  padding: 32px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  margin-bottom: 32px;
}

.info-title {
  font-size: 24px !important;
  font-weight: bold !important;
  color: #333 !important;
  margin-bottom: 24px !important;
  text-align: center;
}

.info-content {
  margin-top: 20px;
}

.info-item {
  text-align: center;
  padding: 20px;
  border-radius: 8px;
  background: #f8f9fa;
  height: 100%;
  transition: all 0.3s ease;
}

.info-item:hover {
  background: #e9ecef;
  transform: translateY(-2px);
}

.info-icon {
  font-size: 32px;
  margin-bottom: 12px;
  display: block;
}

.info-label {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.info-desc {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

/* 正方形按钮样式 */
.modern-button.ant-btn {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  font-size: 16px;
}

.modern-button.ant-btn.ant-btn-block {
  width: 100%;
  height: 40px;
  padding: 0 16px;
}

/* 页面加载动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.feature-card {
  animation: fadeInUp 0.6s ease forwards;
}

.feature-card:nth-child(1) { animation-delay: 0.1s; }
.feature-card:nth-child(2) { animation-delay: 0.2s; }
.feature-card:nth-child(3) { animation-delay: 0.3s; }
.feature-card:nth-child(4) { animation-delay: 0.4s; }
.feature-card:nth-child(5) { animation-delay: 0.5s; }

.ant-card-head-title {
  font-weight: 600 !important;
  color: #2c3e50 !important;
}

.ant-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  border-radius: 12px !important;
  font-weight: 600 !important;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4) !important;
  transition: all 0.3s ease !important;
}

.ant-btn-primary:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6) !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.ant-table {
  background: transparent !important;
}

.ant-table-thead > tr > th {
  background: rgba(255, 255, 255, 0.8) !important;
  backdrop-filter: blur(10px) !important;
  border-bottom: 2px solid rgba(102, 126, 234, 0.2) !important;
  font-weight: 600 !important;
  color: #2c3e50 !important;
}

.ant-table-tbody > tr > td {
  background: rgba(255, 255, 255, 0.6) !important;
  backdrop-filter: blur(5px) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.ant-table-tbody > tr:hover > td {
  background: rgba(102, 126, 234, 0.1) !important;
}

.ant-menu {
  background: transparent !important;
}

.ant-menu-item {
  border-radius: 8px !important;
  margin: 4px 8px !important;
  transition: all 0.3s ease !important;
}

.ant-menu-item:hover {
  background: rgba(102, 126, 234, 0.1) !important;
  color: #667eea !important;
}

.ant-menu-item-selected {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2)) !important;
  color: #667eea !important;
  font-weight: 600 !important;
}

.ant-form-item-label > label {
  font-weight: 600 !important;
  color: #2c3e50 !important;
}

.ant-input, .ant-select-selector {
  border-radius: 8px !important;
  border: 1px solid rgba(102, 126, 234, 0.3) !important;
  transition: all 0.3s ease !important;
}

.ant-input:focus, .ant-select-focused .ant-select-selector {
  border-color: #667eea !important;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2) !important;
}

.ant-tag {
  border-radius: 20px !important;
  font-weight: 500 !important;
  padding: 4px 12px !important;
  border: none !important;
}

.ant-statistic-content {
  color: #2c3e50 !important;
}

.ant-statistic-title {
  color: #64748b !important;
  font-weight: 500 !important;
}