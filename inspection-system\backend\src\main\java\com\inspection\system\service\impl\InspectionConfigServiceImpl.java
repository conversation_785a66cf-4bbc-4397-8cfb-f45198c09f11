package com.inspection.system.service.impl;

import com.inspection.system.dto.InspectionConfigDTO;
import com.inspection.system.dto.PageResponse;
import com.inspection.system.entity.InspectionConfig;
import com.inspection.system.repository.InspectionConfigRepository;
import com.inspection.system.service.InspectionConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class InspectionConfigServiceImpl implements InspectionConfigService {

    private final InspectionConfigRepository configRepository;

    @Override
    public PageResponse<InspectionConfigDTO> getConfigsPage(String configName, String targetType, String frequencyType, String status, Boolean autoInspection, Pageable pageable) {
        InspectionConfig.TargetType targetEnum = targetType != null ? InspectionConfig.TargetType.valueOf(targetType) : null;
        InspectionConfig.FrequencyType freqEnum = frequencyType != null ? InspectionConfig.FrequencyType.valueOf(frequencyType) : null;
        InspectionConfig.ConfigStatus statusEnum = status != null ? InspectionConfig.ConfigStatus.valueOf(status) : null;

        List<InspectionConfig> configs = configRepository.findByConditions(configName, targetEnum, freqEnum, statusEnum, autoInspection);
        long total = configs.size();
        int start = (int) pageable.getOffset();
        int end = Math.min(start + pageable.getPageSize(), (int) total);
        List<InspectionConfig> paged = configs.subList(start, end);

        List<InspectionConfigDTO> dtos = paged.stream().map(this::convertToDTO).collect(Collectors.toList());
        return new PageResponse<InspectionConfigDTO>(dtos, pageable.getPageNumber() + 1, pageable.getPageSize(), total);
    }

    @Override
    public InspectionConfigDTO getConfigById(Long id) {
        return configRepository.findById(id).map(this::convertToDTO).orElse(null);
    }

    @Override
    public InspectionConfigDTO createConfig(InspectionConfigDTO configDTO) {
        InspectionConfig config = convertToEntity(configDTO);
        config.setCreatedAt(LocalDateTime.now());
        config.setUpdatedAt(LocalDateTime.now());
        config = configRepository.save(config);
        return convertToDTO(config);
    }

    @Override
    public InspectionConfigDTO updateConfig(Long id, InspectionConfigDTO configDTO) {
        return configRepository.findById(id).map(config -> {
            updateEntityFromDTO(config, configDTO);
            config.setUpdatedAt(LocalDateTime.now());
            return convertToDTO(configRepository.save(config));
        }).orElse(null);
    }

    @Override
    public void deleteConfig(Long id) {
        configRepository.deleteById(id);
    }

    @Override
    public InspectionConfigDTO toggleConfigStatus(Long id, String operator) {
        return configRepository.findById(id).map(config -> {
            config.setStatus(config.getStatus() == InspectionConfig.ConfigStatus.ACTIVE ? InspectionConfig.ConfigStatus.INACTIVE : InspectionConfig.ConfigStatus.ACTIVE);
            config.setUpdatedBy(operator);
            config.setUpdatedAt(LocalDateTime.now());
            return convertToDTO(configRepository.save(config));
        }).orElse(null);
    }

    @Override
    public InspectionConfigDTO toggleAutoInspection(Long id, String operator) {
        return configRepository.findById(id).map(config -> {
            config.setAutoInspection(!config.getAutoInspection());
            config.setUpdatedBy(operator);
            config.setUpdatedAt(LocalDateTime.now());
            return convertToDTO(configRepository.save(config));
        }).orElse(null);
    }

    @Override
    public void batchUpdateConfigStatus(List<Long> configIds, InspectionConfig.ConfigStatus status, String operator) {
        List<InspectionConfig> configs = configRepository.findAllById(configIds);
        configs.forEach(config -> {
            config.setStatus(status);
            config.setUpdatedBy(operator);
            config.setUpdatedAt(LocalDateTime.now());
        });
        configRepository.saveAll(configs);
    }

    @Override
    public List<InspectionConfigDTO> getActiveConfigs() {
        return configRepository.findByStatus(InspectionConfig.ConfigStatus.ACTIVE).stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    @Override
    public List<Object[]> getConfigStats() {
        return configRepository.getConfigStatsByTargetType();
    }

    @Override
    public List<InspectionConfigDTO> getConfigsByTarget(String targetType, Long targetId) {
        InspectionConfig.TargetType typeEnum = InspectionConfig.TargetType.valueOf(targetType);
        return configRepository.findByTargetTypeAndTargetId(typeEnum, targetId).stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    @Override
    public List<InspectionConfigDTO> getAutoInspectionConfigs() {
        return configRepository.findByAutoInspectionTrueAndStatus(InspectionConfig.ConfigStatus.ACTIVE).stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    private InspectionConfigDTO convertToDTO(InspectionConfig config) {
        InspectionConfigDTO dto = new InspectionConfigDTO();
        dto.setId(config.getId());
        dto.setConfigName(config.getConfigName());
        dto.setTargetType(config.getTargetType().name());
        dto.setTargetId(config.getTargetId());
        dto.setFrequencyType(config.getFrequencyType().name());
        dto.setFrequencyValue(config.getFrequencyValue());
        dto.setCronExpression(config.getCronExpression());
        dto.setInspectionItems(config.getInspectionItems());
        dto.setAutoInspection(config.getAutoInspection());
        dto.setStatus(config.getStatus().name());
        dto.setCreatedAt(config.getCreatedAt());
        dto.setUpdatedAt(config.getUpdatedAt());
        dto.setCreatedBy(config.getCreatedBy());
        dto.setUpdatedBy(config.getUpdatedBy());
        return dto;
    }

    private InspectionConfig convertToEntity(InspectionConfigDTO dto) {
        InspectionConfig config = new InspectionConfig();
        updateEntityFromDTO(config, dto);
        return config;
    }

    private void updateEntityFromDTO(InspectionConfig config, InspectionConfigDTO dto) {
        config.setConfigName(dto.getConfigName());
        config.setTargetType(InspectionConfig.TargetType.valueOf(dto.getTargetType()));
        config.setTargetId(dto.getTargetId());
        config.setFrequencyType(InspectionConfig.FrequencyType.valueOf(dto.getFrequencyType()));
        config.setFrequencyValue(dto.getFrequencyValue());
        config.setCronExpression(dto.getCronExpression());
        config.setInspectionItems(dto.getInspectionItems());
        config.setAutoInspection(dto.getAutoInspection());
        config.setStatus(InspectionConfig.ConfigStatus.valueOf(dto.getStatus()));
    }
}