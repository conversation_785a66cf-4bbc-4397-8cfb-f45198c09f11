package com.inspection.system.service.impl;

import com.inspection.system.dto.InspectionTaskDTO;
import com.inspection.system.entity.InspectionConfig;
import com.inspection.system.entity.InspectionTask;
import com.inspection.system.repository.InspectionConfigRepository;
import com.inspection.system.repository.InspectionTaskRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 巡检任务服务实现类测试
 */
@ExtendWith(MockitoExtension.class)
class InspectionTaskServiceImplTest {

    @Mock
    private InspectionTaskRepository taskRepository;

    @Mock
    private InspectionConfigRepository configRepository;

    @InjectMocks
    private InspectionTaskServiceImpl taskService;

    private InspectionTask testTask;
    private InspectionTaskDTO testTaskDTO;
    private InspectionConfig testConfig;

    @BeforeEach
    void setUp() {
        // 准备测试配置
        testConfig = new InspectionConfig();
        testConfig.setId(1L);
        testConfig.setConfigName("测试巡检配置");
        testConfig.setTargetType(InspectionConfig.TargetType.DEVICE);
        testConfig.setTargetId(1L);
        testConfig.setStatus(InspectionConfig.ConfigStatus.ACTIVE);

        // 准备测试任务
        testTask = new InspectionTask();
        testTask.setId(1L);
        testTask.setTaskCode("TASK001");
        testTask.setTaskName("测试巡检任务");
        testTask.setStatus(InspectionTask.TaskStatus.PENDING);
        testTask.setTaskType(InspectionTask.TaskType.AUTO);
        testTask.setPriority(InspectionTask.TaskPriority.HIGH);
        testTask.setScheduledTime(LocalDateTime.now());
        testTask.setConfigId(1L);
        testTask.setTargetType(InspectionTask.TargetType.DEVICE);
        testTask.setTargetId(1L);
        testTask.setCreateTime(LocalDateTime.now());
        testTask.setUpdateTime(LocalDateTime.now());

        // 准备测试DTO
        testTaskDTO = new InspectionTaskDTO();
        testTaskDTO.setId(1L);
        testTaskDTO.setTaskCode("TASK001");
        testTaskDTO.setTaskName("测试巡检任务");
        testTaskDTO.setStatus(InspectionTask.TaskStatus.PENDING);
        testTaskDTO.setTaskType(InspectionTask.TaskType.AUTO);
        testTaskDTO.setPriority(InspectionTask.TaskPriority.HIGH);
        testTaskDTO.setScheduledTime(LocalDateTime.now());
        testTaskDTO.setConfigId(1L);
    }

    @Test
    void testGetTasks() {
        // 准备数据
        List<InspectionTask> tasks = Arrays.asList(testTask);
        Page<InspectionTask> page = new PageImpl<>(tasks, PageRequest.of(0, 10), 1);
        
        when(taskRepository.findAll(any(Specification.class), any(Pageable.class)))
                .thenReturn(page);

        // 执行测试
        Page<InspectionTaskDTO> result = taskService.getTasks(
                PageRequest.of(0, 10), "测试", 
                InspectionTask.TaskStatus.PENDING,
                InspectionTask.TaskType.AUTO,
                InspectionTask.TaskPriority.HIGH,
                LocalDateTime.now().minusDays(1),
                LocalDateTime.now().plusDays(1)
        );

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals(1, result.getContent().size());
        assertEquals("TASK001", result.getContent().get(0).getTaskCode());
        assertEquals("测试巡检任务", result.getContent().get(0).getTaskName());

        // 验证调用
        verify(taskRepository).findAll(any(Specification.class), any(Pageable.class));
    }

    @Test
    void testGetTaskById() {
        // 准备数据
        when(taskRepository.findById(1L)).thenReturn(Optional.of(testTask));

        // 执行测试
        InspectionTaskDTO result = taskService.getTaskById(1L);

        // 验证结果
        assertNotNull(result);
        assertEquals(1L, result.getId());
        assertEquals("TASK001", result.getTaskCode());
        assertEquals("测试巡检任务", result.getTaskName());

        // 验证调用
        verify(taskRepository).findById(1L);
    }

    @Test
    void testGetTaskByIdNotFound() {
        // 准备数据
        when(taskRepository.findById(999L)).thenReturn(Optional.empty());

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            taskService.getTaskById(999L);
        });

        // 验证调用
        verify(taskRepository).findById(999L);
    }

    @Test
    void testCreateTask() {
        // 准备数据
        when(configRepository.findById(1L)).thenReturn(Optional.of(testConfig));
        when(taskRepository.save(any(InspectionTask.class))).thenReturn(testTask);

        // 执行测试
        InspectionTaskDTO result = taskService.createTask(testTaskDTO);

        // 验证结果
        assertNotNull(result);
        assertEquals("TASK001", result.getTaskCode());
        assertEquals("测试巡检任务", result.getTaskName());

        // 验证调用
        verify(configRepository).findById(1L);
        verify(taskRepository).save(any(InspectionTask.class));
    }

    @Test
    void testCreateTaskWithInvalidConfig() {
        // 准备数据
        when(configRepository.findById(999L)).thenReturn(Optional.empty());
        testTaskDTO.setConfigId(999L);

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            taskService.createTask(testTaskDTO);
        });

        // 验证调用
        verify(configRepository).findById(999L);
        verify(taskRepository, never()).save(any(InspectionTask.class));
    }

    @Test
    void testUpdateTask() {
        // 准备数据
        when(taskRepository.findById(1L)).thenReturn(Optional.of(testTask));
        when(taskRepository.save(any(InspectionTask.class))).thenReturn(testTask);
        
        testTaskDTO.setTaskName("更新后的任务名称");
        testTaskDTO.setStatus(InspectionTask.TaskStatus.COMPLETED);

        // 执行测试
        InspectionTaskDTO result = taskService.updateTask(1L, testTaskDTO);

        // 验证结果
        assertNotNull(result);
        assertEquals(1L, result.getId());

        // 验证调用
        verify(taskRepository).findById(1L);
        verify(taskRepository).save(any(InspectionTask.class));
    }

    @Test
    void testUpdateTaskNotFound() {
        // 准备数据
        when(taskRepository.findById(999L)).thenReturn(Optional.empty());

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            taskService.updateTask(999L, testTaskDTO);
        });

        // 验证调用
        verify(taskRepository).findById(999L);
        verify(taskRepository, never()).save(any(InspectionTask.class));
    }

    @Test
    void testDeleteTask() {
        // 准备数据
        when(taskRepository.findById(1L)).thenReturn(Optional.of(testTask));
        doNothing().when(taskRepository).delete(testTask);

        // 执行测试
        assertDoesNotThrow(() -> {
            taskService.deleteTask(1L);
        });

        // 验证调用
        verify(taskRepository).findById(1L);
        verify(taskRepository).delete(testTask);
    }

    @Test
    void testDeleteTaskNotFound() {
        // 准备数据
        when(taskRepository.findById(999L)).thenReturn(Optional.empty());

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            taskService.deleteTask(999L);
        });

        // 验证调用
        verify(taskRepository).findById(999L);
        verify(taskRepository, never()).delete(any(InspectionTask.class));
    }

    @Test
    void testBatchDeleteTasks() {
        // 准备数据
        List<Long> taskIds = Arrays.asList(1L, 2L, 3L);
        InspectionTask task2 = new InspectionTask();
        task2.setId(2L);
        InspectionTask task3 = new InspectionTask();
        task3.setId(3L);
        
        when(taskRepository.findById(1L)).thenReturn(Optional.of(testTask));
        when(taskRepository.findById(2L)).thenReturn(Optional.of(task2));
        when(taskRepository.findById(3L)).thenReturn(Optional.of(task3));
        doNothing().when(taskRepository).delete(any(InspectionTask.class));

        // 执行测试
        assertDoesNotThrow(() -> {
            taskService.batchDeleteTasks(taskIds);
        });

        // 验证调用
        verify(taskRepository, times(3)).findById(anyLong());
        verify(taskRepository, times(3)).delete(any(InspectionTask.class));
    }

    @Test
    void testExecuteTask() {
        // 准备数据
        when(taskRepository.findById(1L)).thenReturn(Optional.of(testTask));
        when(taskRepository.save(any(InspectionTask.class))).thenReturn(testTask);

        // 执行测试
        assertDoesNotThrow(() -> {
            taskService.executeTask(1L);
        });

        // 验证调用
        verify(taskRepository).findById(1L);
        verify(taskRepository).save(any(InspectionTask.class));
    }

    @Test
    void testExecuteTaskNotFound() {
        // 准备数据
        when(taskRepository.findById(999L)).thenReturn(Optional.empty());

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            taskService.executeTask(999L);
        });

        // 验证调用
        verify(taskRepository).findById(999L);
        verify(taskRepository, never()).save(any(InspectionTask.class));
    }

    @Test
    void testExecuteTaskAlreadyCompleted() {
        // 准备数据
        testTask.setStatus(InspectionTask.TaskStatus.COMPLETED);
        when(taskRepository.findById(1L)).thenReturn(Optional.of(testTask));

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            taskService.executeTask(1L);
        });

        // 验证调用
        verify(taskRepository).findById(1L);
        verify(taskRepository, never()).save(any(InspectionTask.class));
    }

    @Test
    void testGetTasksWithNullFilters() {
        // 准备数据
        List<InspectionTask> tasks = Arrays.asList(testTask);
        Page<InspectionTask> page = new PageImpl<>(tasks, PageRequest.of(0, 10), 1);
        
        when(taskRepository.findAll(any(Specification.class), any(Pageable.class)))
                .thenReturn(page);

        // 执行测试 - 所有过滤条件为null
        Page<InspectionTaskDTO> result = taskService.getTasks(
                PageRequest.of(0, 10), null, null, null, null, null, null
        );

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());

        // 验证调用
        verify(taskRepository).findAll(any(Specification.class), any(Pageable.class));
    }

    @Test
    void testCreateTaskGeneratesTaskCode() {
        // 准备数据
        when(configRepository.findById(1L)).thenReturn(Optional.of(testConfig));
        when(taskRepository.save(any(InspectionTask.class))).thenAnswer(invocation -> {
            InspectionTask task = invocation.getArgument(0);
            task.setId(1L);
            return task;
        });
        
        testTaskDTO.setTaskCode(null); // 不设置任务编码，让系统自动生成

        // 执行测试
        InspectionTaskDTO result = taskService.createTask(testTaskDTO);

        // 验证结果
        assertNotNull(result);
        // 验证任务编码已生成（应该以TASK开头）
        // 具体的编码生成逻辑需要根据实际实现来验证

        // 验证调用
        verify(configRepository).findById(1L);
        verify(taskRepository).save(any(InspectionTask.class));
    }
}