@echo off
echo Starting Inspection Management System...
echo.

REM Set environment variables
set JAVA_HOME=E:\jdk\jdk-17.0.11
set PATH=%PATH%;E:\jdk\jdk-17.0.11\bin;E:\maven\apache-maven-3.9.6\bin

echo Starting backend service (port 8003)...
start "Backend Service" cmd /c "cd /d %~dp0backend && E:\maven\apache-maven-3.9.6\bin\mvn.cmd spring-boot:run"

echo Waiting 5 seconds before starting frontend...
timeout /t 5 > nul

echo Starting frontend application (port 3000)...
start "Frontend App" cmd /c "cd /d %~dp0frontend && npm start"

echo.
echo Startup completed!
echo Frontend URL: http://localhost:3003
echo Backend API: http://localhost:8003/api
echo API Documentation: http://localhost:8003/api/swagger-ui.html
echo.
pause